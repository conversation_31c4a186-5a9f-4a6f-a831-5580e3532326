# 添加商品SKU字段

## 修改说明

在`getShippingPageData`接口的返回数据中添加了`productSku`字段，商品编码使用`sku_id`字段而不是`product_code`字段。

## 字段映射修改

### 拆分订单商品数据

```javascript
responseData.products = splitOrder.split_items.map(item => ({
  id: item.id,
  productId: item.product_id,
  productCode: item.sku_id, // 修改：使用sku_id作为商品编码
  productSku: item.sku_id,  // 新增：商品SKU字段
  productName: item.product_name,
  productCategory: item.product_category,
  productBrand: item.product_brand,
  quantity: item.quantity,
  unitPrice: item.unit_price,
  totalPrice: item.total_price,
  specifications: item.specifications,
  productImage: item.product_image,
}));
```

### 原订单商品数据

```javascript
responseData.products = purchaseOrderItems.map(item => ({
  id: item.id,
  productId: item.product_id,
  productCode: item.sku_id, // 修改：使用sku_id作为商品编码
  productSku: item.sku_id,  // 新增：商品SKU字段
  productName: item.product_name,
  productCategory: item.product_category,
  productBrand: item.product_brand,
  quantity: item.quantity,
  unitPrice: item.unit_price,
  totalPrice: item.total_price,
  specifications: item.specifications,
  productImage: item.product_image,
  purchaseProgress: item.purchase_progress,
  expectedDeliveryTime: item.expected_delivery_time,
}));
```

## API响应示例

```json
{
  "code": 200,
  "message": "获取发货页面数据成功",
  "data": {
    "linkId": 2,
    "shippingLink": "https://frontend.com/h5/SupplyChain/HavedShipped/2",
    "qrCodeUrl": "https://backend.com/uploads/qrcodes/shipping/qrcode_PO123456_2_1641801600000.png",
    "receiverName": "张三",
    "receiverPhone": "13800138000",
    "receiverAddress": "北京市朝阳区xxx街道xxx号",
    "orderInfo": {
      "orderType": "purchase",
      "orderNumber": "PO123456",
      "createTime": "2025-01-10T10:30:00.000Z"
    },
    "products": [
      {
        "id": 1,
        "productId": 789,
        "productCode": "SKU123456", // 使用sku_id
        "productSku": "SKU123456",  // 新增字段
        "productName": "商品名称",
        "productCategory": "模拟器材",
        "productBrand": "品牌名称",
        "quantity": 2,
        "unitPrice": 500.00,
        "totalPrice": 1000.00,
        "specifications": "规格信息",
        "productImage": "https://example.com/image.jpg"
      }
    ]
  }
}
```

## 字段说明

| 字段名 | 数据来源 | 说明 |
|--------|----------|------|
| `productCode` | `item.sku_id` | 商品编码，使用SKU ID |
| `productSku` | `item.sku_id` | 商品SKU，与商品编码相同 |

## 影响范围

1. **H5发货页面**：可以正确显示商品SKU信息
2. **前端显示**：商品编号显示为实际的SKU ID
3. **数据一致性**：确保商品编码和SKU字段的一致性

## 测试验证

```bash
# 测试接口返回数据
curl -X GET "http://localhost:4000/api/v1/master/csm/purchase-orders/shipping-page/2"

# 验证返回数据中包含：
# - productCode: 使用sku_id的值
# - productSku: 使用sku_id的值
# - 两个字段值相同
```

这个修改确保了商品编码和SKU字段正确映射到数据库中的`sku_id`字段。
