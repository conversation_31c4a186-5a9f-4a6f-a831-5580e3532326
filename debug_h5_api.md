# H5 API调试指南

## 问题描述
H5页面调用API时出现错误：`TypeError: request is not a function`

## 可能的原因和解决方案

### 1. 导入路径问题
**检查项**：
- ✅ 修改了导入路径从 `~/api/h5/supplyChain.js` 到 `@/api/h5/supplyChain.js`
- ✅ 确保与其他H5页面的导入方式一致

### 2. request函数导入问题
**检查项**：
- ✅ 修改了API文件中的导入：`import { request } from '@/utils/request.js'`
- ✅ 确保与其他API文件的导入方式一致

### 3. 调试步骤

#### 步骤1：检查API文件导入
在浏览器控制台中查看：
```javascript
console.log('supplyChainApi:', supplyChainApi)
console.log('getShippingPageData方法:', supplyChainApi.getShippingPageData)
```

#### 步骤2：检查request函数
在API文件中添加调试：
```javascript
console.log('API调用 - request函数:', typeof request)
```

#### 步骤3：检查网络请求
在浏览器开发者工具的Network标签中查看是否有API请求发出。

### 4. 备用解决方案

如果问题仍然存在，可以尝试以下方案：

#### 方案A：直接使用axios
```javascript
import axios from 'axios'

const response = await axios.get(`/api/v1/master/csm/purchase-orders/shipping-page/${linkId}`)
```

#### 方案B：使用$fetch (Nuxt3)
```javascript
const response = await $fetch(`/api/v1/master/csm/purchase-orders/shipping-page/${linkId}`)
```

#### 方案C：检查Nuxt配置
确保在`nuxt.config.js`中正确配置了API代理。

### 5. 测试URL

测试时可以使用以下URL格式：
```
http://localhost:3000/h5/SupplyChain/HavedShipped/123
```

其中`123`是发货链接ID。

### 6. 后端API测试

可以直接测试后端API：
```bash
curl -X GET "http://localhost:4000/api/v1/master/csm/purchase-orders/shipping-page/123"
```

### 7. 错误处理

页面现在包含了完整的错误处理：
- 加载状态显示
- 错误状态显示
- 重试功能
- 详细的控制台日志

### 8. 调试日志

页面会输出以下调试信息：
- 链接ID
- API对象
- API方法
- 请求URL
- 响应数据
- 错误信息

### 9. 下一步

1. 打开浏览器开发者工具
2. 访问H5页面
3. 查看控制台输出
4. 检查Network标签中的请求
5. 根据错误信息进行相应的修复

## 常见错误和解决方案

### 错误1：`request is not a function`
**解决方案**：检查导入路径和导入方式

### 错误2：`404 Not Found`
**解决方案**：检查后端API路由是否正确配置

### 错误3：`CORS错误`
**解决方案**：检查后端CORS配置

### 错误4：`网络错误`
**解决方案**：检查后端服务是否启动，端口是否正确

## 成功标志

当一切正常时，控制台应该显示：
```
开始获取发货数据，链接ID: 123
API调用 - 链接ID: 123
API调用 - request函数: function
API调用 - 完整URL: /api/v1/master/csm/purchase-orders/shipping-page/123
API响应: { code: 200, message: "获取发货页面数据成功", data: {...} }
数据设置完成: { orderData: {...}, products: [...] }
```
