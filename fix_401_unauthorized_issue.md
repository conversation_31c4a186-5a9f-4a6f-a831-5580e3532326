# 修复401未授权错误

## 问题描述

H5发货页面接口 `GET /api/v1/master/csm/purchase-orders/shipping-page/{linkId}` 返回401未授权错误，即使该接口应该是公开访问的。

## 问题原因

1. **路由注册顺序问题**：
   - CSM路由通过 `router.use('/csm', createCsmRouter(prisma))` 注册
   - 这个注册在主路由文件的第312行
   - 而认证中间件 `router.use(authMiddleware)` 在第189行
   - 所有在认证中间件之后注册的路由都会被强制要求token验证

2. **中间件影响范围**：
   ```javascript
   // server/apps/master/routes/index.js
   router.use(authMiddleware);  // 第189行
   // ... 其他路由
   router.use('/csm', createCsmRouter(prisma));  // 第312行 - 受认证中间件影响
   ```

## 解决方案

### 方案1：移动接口到认证中间件之前（已采用）

将H5发货页面接口从CSM路由中移出，直接在主路由文件中注册，确保在认证中间件之前：

```javascript
// server/apps/master/routes/index.js

// H5发货页面公开接口（无需鉴权）
const { prisma } = require('../../../core/database/prisma');
const PurchaseOrderController = require('../csm/controllers/PurchaseOrderController');
const h5ShippingController = new PurchaseOrderController(prisma);
router.get('/csm/purchase-orders/shipping-page/:linkId', (req, res) => {
  h5ShippingController.getShippingPageData(req, res);
});

// ... 其他公开路由

router.use(authMiddleware);  // 认证中间件

// ... 需要认证的路由
router.use('/csm', createCsmRouter(prisma));
```

### 方案2：条件性认证中间件（备选）

```javascript
// 为特定路径跳过认证
router.use((req, res, next) => {
  if (req.path.includes('/shipping-page/')) {
    return next();
  }
  return authMiddleware(req, res, next);
});
```

## 修改内容

### 1. 主路由文件修改

**文件**：`server/apps/master/routes/index.js`

**修改位置**：第166-179行

**添加内容**：
```javascript
// H5发货页面公开接口（无需鉴权）
const { prisma } = require('../../../core/database/prisma');
const PurchaseOrderController = require('../csm/controllers/PurchaseOrderController');
const h5ShippingController = new PurchaseOrderController(prisma);
router.get('/csm/purchase-orders/shipping-page/:linkId', (req, res) => {
  h5ShippingController.getShippingPageData(req, res);
});
```

### 2. CSM路由文件修改

**文件**：`server/apps/master/csm/routes/PurchaseOrderRoute.js`

**修改内容**：移除了重复的H5发货页面接口定义，避免路由冲突

## 验证方法

### 1. 直接API测试

```bash
# 无token访问测试
curl -X GET "http://localhost:4000/api/v1/master/csm/purchase-orders/shipping-page/2"

# 预期结果：
# - 200状态码（如果链接ID存在）
# - 404状态码（如果链接ID不存在）
# - 不应该返回401未授权错误
```

### 2. H5页面测试

```
http://localhost:3000/h5/SupplyChain/HavedShipped/2
```

### 3. 浏览器开发者工具验证

1. 打开Network标签
2. 访问H5页面
3. 检查API请求：
   - 请求头中没有Authorization字段
   - 请求成功返回数据
   - 状态码为200

## 接口对比

| 接口路径 | 访问权限 | 注册位置 | 用途 |
|----------|----------|----------|------|
| `GET /csm/purchase-orders/shipping-page/{linkId}` | **公开** | 主路由文件（认证中间件前） | H5页面获取发货信息 |
| `GET /csm/purchase-orders/{id}/shipping-link` | 需要token | CSM路由（认证中间件后） | 管理后台获取发货链接 |
| `POST /csm/purchase-orders/{id}/generate-shipping-link` | 需要token | CSM路由（认证中间件后） | 管理后台生成发货链接 |

## 安全考虑

1. **数据保护**：
   - 使用链接ID而不是订单号，避免信息泄露
   - 只返回发货相关的必要信息

2. **访问控制**：
   - 只能通过有效链接ID访问
   - 无效ID返回404错误

3. **监控建议**：
   - 记录访问日志
   - 监控异常访问频率
   - 设置适当的缓存策略

## 测试结果

修复后，接口应该：
- ✅ 无需token即可访问
- ✅ 返回正确的发货数据
- ✅ H5页面正常显示
- ✅ 不会出现401未授权错误

## 注意事项

1. **生产环境部署**：确保修改同步到生产环境
2. **缓存清理**：可能需要清理相关缓存
3. **监控告警**：关注接口访问情况和错误率
4. **文档更新**：更新API文档，明确标注公开接口

这个修复确保了H5发货页面可以被任何人访问，同时保持了其他管理接口的安全性。
