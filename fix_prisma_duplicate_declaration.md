# 修复Prisma重复声明错误

## 问题描述

服务器启动时出现错误：
```
SyntaxError: Identifier 'prisma' has already been declared
```

## 问题原因

在主路由文件 `server/apps/master/routes/index.js` 中，`prisma` 变量被重复声明：

1. **第一次声明**：第294行（用于商城新闻分类管理）
2. **第二次声明**：第170行（我添加的H5发货页面接口）

```javascript
// 第170行 - 重复声明
const { prisma } = require('../../../core/database/prisma');

// 第294行 - 原有声明
const { prisma } = require('../../../core/database/prisma');
```

## 解决方案

### 方案1：延迟初始化（已采用）

将H5发货页面接口移到认证中间件之前，使用延迟初始化避免重复声明：

```javascript
// H5发货页面公开接口（无需鉴权）
const PurchaseOrderController = require('../csm/controllers/PurchaseOrderController');
let h5ShippingController;
router.get('/csm/purchase-orders/shipping-page/:linkId', (req, res) => {
  if (!h5ShippingController) {
    const { prisma } = require('../../../core/database/prisma');
    h5ShippingController = new PurchaseOrderController(prisma);
  }
  h5ShippingController.getShippingPageData(req, res);
});

router.use(authMiddleware); // 认证中间件
```

### 方案2：使用已声明的变量（备选）

```javascript
// 在prisma声明之后添加路由
const { prisma } = require('../../../core/database/prisma');
const PurchaseOrderController = require('../csm/controllers/PurchaseOrderController');
const h5ShippingController = new PurchaseOrderController(prisma);

// 但这样会在认证中间件之后，不符合公开接口的要求
```

## 修改内容

### 1. 移除重复声明

**文件**：`server/apps/master/routes/index.js`

**移除位置**：第169-170行
```javascript
// 移除这些行
// H5发货页面公开接口（无需鉴权）
const { prisma } = require('../../../core/database/prisma');
```

### 2. 添加延迟初始化的路由

**文件**：`server/apps/master/routes/index.js`

**添加位置**：第191-201行（认证中间件之前）
```javascript
// H5发货页面公开接口（无需鉴权）
const PurchaseOrderController = require('../csm/controllers/PurchaseOrderController');
let h5ShippingController;
router.get('/csm/purchase-orders/shipping-page/:linkId', (req, res) => {
  if (!h5ShippingController) {
    const { prisma } = require('../../../core/database/prisma');
    h5ShippingController = new PurchaseOrderController(prisma);
  }
  h5ShippingController.getShippingPageData(req, res);
});
```

## 优势

1. **避免重复声明**：不会与现有的prisma声明冲突
2. **延迟加载**：只在需要时才初始化控制器
3. **性能优化**：控制器只初始化一次，后续请求复用
4. **位置正确**：在认证中间件之前，确保公开访问

## 验证方法

### 1. 服务器启动测试

```bash
# 启动服务器，应该不再出现prisma重复声明错误
npm run dev
```

### 2. 接口访问测试

```bash
# 测试H5发货页面接口
curl -X GET "http://localhost:4000/api/v1/master/csm/purchase-orders/shipping-page/2"
```

### 3. H5页面测试

```
http://localhost:3000/h5/SupplyChain/HavedShipped/2
```

## 路由顺序确认

修复后的路由顺序：

```javascript
// 1. 公开路由（无需认证）
router.use('/express-company-code', expressCompanyCodeRoutes);
router.use('/invoices', invoiceRoutes);
// ... 其他公开路由

// 2. H5发货页面公开接口（新增）
router.get('/csm/purchase-orders/shipping-page/:linkId', ...);

// 3. 认证中间件
router.use(authMiddleware);

// 4. 需要认证的路由
router.use('/goods-spu', goodsSpuRoutes);
// ... 其他需要认证的路由
router.use('/csm', createCsmRouter(prisma));
```

## 注意事项

1. **延迟初始化**：控制器在第一次请求时才初始化
2. **单例模式**：控制器只初始化一次，提高性能
3. **错误处理**：如果prisma初始化失败，会在运行时报错
4. **内存管理**：控制器实例会保持在内存中

## 测试清单

- [ ] 服务器正常启动，无prisma重复声明错误
- [ ] H5发货页面接口可以无token访问
- [ ] 接口返回正确的数据格式
- [ ] H5页面正常显示发货信息
- [ ] 其他CSM接口仍需要token验证

这个修复确保了服务器可以正常启动，同时保持了H5发货页面接口的公开访问特性。
