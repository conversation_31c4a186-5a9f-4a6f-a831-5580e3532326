# 修复发货页面查询逻辑

## 问题描述

原始查询中存在错误的字段引用：
```
Unknown field `product` for include statement on model purchase_order_split_item
```

## 问题原因

在查询发货链接信息时，试图在`purchase_order_split_item`模型上使用不存在的`product`关联字段。

## 解决方案

### 修改前的查询逻辑

```javascript
// 错误的查询方式 - 试图一次性查询所有关联数据
const shippingLink = await this.prisma.purchase_order_shipping_link.findUnique({
  where: { id: parseInt(linkId) },
  include: {
    purchase_order: {
      include: {
        original_order: true,
      },
    },
    split_order: {
      include: {
        split_items: {
          include: {
            product: true, // 错误：split_items没有product关联
          },
        },
      },
    },
  },
});
```

### 修改后的查询逻辑

```javascript
// 1. 先查询发货链接基本信息
const shippingLink = await this.prisma.purchase_order_shipping_link.findUnique({
  where: { id: parseInt(linkId) },
  include: {
    purchase_order: true, // 只包含采购订单基本信息
  },
});

// 2. 根据是否存在拆分订单ID来决定查询策略
if (shippingLink.split_order_id) {
  // 存在拆分订单ID，单独查询拆分订单数据
  const splitOrder = await this.prisma.purchase_order_split.findUnique({
    where: { id: shippingLink.split_order_id },
    include: {
      split_items: true, // 包含拆分商品项
    },
  });
  // 处理拆分订单数据...
} else {
  // 不存在拆分订单ID，查询原订单商品项
  const purchaseOrderItems = await this.prisma.purchase_order_item.findMany({
    where: { purchase_order_id: purchaseOrder.id },
  });
  // 处理原订单数据...
}
```

## 核心改进

### 1. 条件查询策略

- **判断条件**：`shippingLink.split_order_id`
- **存在拆分订单ID**：查询拆分订单的商品数据
- **不存在拆分订单ID**：查询原订单的商品数据

### 2. 分离查询逻辑

- **第一步**：查询发货链接基本信息
- **第二步**：根据条件决定查询拆分订单还是原订单
- **第三步**：组装返回数据

### 3. 数据结构优化

```javascript
// 拆分订单数据结构
if (shippingLink.split_order_id) {
  responseData.orderInfo = {
    orderType: 'split',
    orderId: splitOrder.id,
    orderNumber: splitOrder.split_order_number,
    createTime: splitOrder.created_at,
    totalAmount: splitOrder.total_amount,
    status: splitOrder.status,
  };
  
  responseData.products = splitOrder.split_items.map(item => ({
    id: item.id,
    productId: item.product_id,
    productCode: item.product_code,
    productSku: item.product_code, // 商品编码就是商品SKU
    productName: item.product_name,
    // ... 其他字段
  }));
}

// 原订单数据结构
else {
  responseData.orderInfo = {
    orderType: 'purchase',
    orderId: purchaseOrder.id,
    orderNumber: purchaseOrder.purchase_order_number,
    originalOrderNumber: purchaseOrder.original_order_number,
    // ... 其他字段
  };
  
  responseData.products = purchaseOrderItems.map(item => ({
    id: item.id,
    productId: item.product_id,
    productCode: item.product_code,
    productSku: item.product_code, // 商品编码就是商品SKU
    productName: item.product_name,
    // ... 其他字段
  }));
}
```

## 业务逻辑

### 拆分订单场景

1. **触发条件**：`split_order_id`字段不为空
2. **查询目标**：`purchase_order_split`表和`purchase_order_split_item`表
3. **返回数据**：拆分订单的特定商品信息

### 原订单场景

1. **触发条件**：`split_order_id`字段为空
2. **查询目标**：`purchase_order_item`表
3. **返回数据**：采购订单的完整商品信息

## 优势

1. **避免错误查询**：不再尝试查询不存在的关联字段
2. **条件查询**：根据实际情况选择查询策略
3. **性能优化**：只查询需要的数据，减少不必要的关联查询
4. **逻辑清晰**：分离查询逻辑，便于维护和调试

## 测试验证

### 测试用例1：拆分订单发货链接

```bash
# 请求包含split_order_id的发货链接
curl -X GET "http://localhost:4000/api/v1/master/csm/purchase-orders/shipping-page/2"

# 预期结果：
# - orderInfo.orderType = 'split'
# - products数组包含拆分订单的商品
```

### 测试用例2：原订单发货链接

```bash
# 请求不包含split_order_id的发货链接
curl -X GET "http://localhost:4000/api/v1/master/csm/purchase-orders/shipping-page/1"

# 预期结果：
# - orderInfo.orderType = 'purchase'
# - products数组包含原订单的商品
```

## 注意事项

1. **数据完整性**：确保拆分订单数据存在时才查询
2. **错误处理**：处理查询失败的情况
3. **字段映射**：确保返回字段与前端期望一致
4. **性能监控**：关注查询性能，必要时添加索引

这个修复确保了查询逻辑的正确性，同时提供了灵活的数据获取策略。
