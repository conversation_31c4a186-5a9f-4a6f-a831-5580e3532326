<template>
    <div class="haved-shipped-page">
      <!-- 固定顶部日期 -->
      <a-affix :offset-top="0" class="fixed-header">
        <div class="date-header">
          {{ formatDate(orderData.createTime) }}
        </div>
      </a-affix>

      <!-- 页面内容 -->
      <div class="content">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <a-spin size="large" />
          <div class="loading-text">正在加载发货信息...</div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
          <div class="error-icon">⚠️</div>
          <div class="error-text">{{ error }}</div>
          <a-button type="primary" @click="fetchShippingData">重试</a-button>
        </div>

        <!-- 页面内容 -->
        <div v-else>
          <!-- 订单号 -->
          <div class="order-number">
            订单号: {{ orderData.orderNo }}
          </div>

        <!-- 收货信息 -->
        <div class="section">
          <h3 class="section-title">收货信息:</h3>
          <div class="info-item">
            <span class="label">收货人:</span>
            <span class="value">{{ orderData.receiverName }}</span>
          </div>
          <div class="info-item">
            <span class="label">配送地址:</span>
            <span class="value">{{ orderData.receiverAddress }}</span>
          </div>
          <div class="info-item">
            <span class="label">收货电话:</span>
            <span class="value">{{ orderData.receiverPhone }}</span>
          </div>
        </div>
        

          <!-- 订单商品 -->
          <div class="section">
            <h3 class="section-title">订单商品:</h3>

            <!-- 如果只有一个商品，显示简化信息 -->
            <div v-if="products.length === 1" class="product-info">
              <div class="info-item clickable" @click="goToProductDetails(products[0])">
                <span class="label">分类:</span>
                <span class="value link-text">{{ products[0].productCategory }}</span>
              </div>
              <div class="info-item clickable" @click="goToProductDetails(products[0])">
                <span class="label">商品编号:</span>
                <span class="value link-text">{{ products[0].productCode }}</span>
              </div>
              <div class="info-item product-name clickable" @click="goToProductDetails(products[0])">
                <span class="label">商品名称:</span>
                <span class="value link-text">{{ products[0].productName }}</span>
              </div>
              <div class="info-item">
                <span class="label">数量:</span>
                <span class="value">{{ products[0].quantity }}</span>
              </div>
            </div>

            <!-- 如果有多个商品，显示商品列表 -->
            <div v-else-if="products.length > 1" class="products-list">
              <div v-for="(product, index) in products" :key="product.id" class="product-item">
                <div class="product-header">
                  <span class="product-index">商品 {{ index + 1 }}</span>
                </div>
                <div class="product-info">
                  <div class="info-item clickable" @click="goToProductDetails(product)">
                    <span class="label">分类:</span>
                    <span class="value link-text">{{ product.productCategory }}</span>
                  </div>
                  <div class="info-item clickable" @click="goToProductDetails(product)">
                    <span class="label">商品编号:</span>
                    <span class="value link-text">{{ product.productCode }}</span>
                  </div>
                  <div class="info-item product-name clickable" @click="goToProductDetails(product)">
                    <span class="label">商品名称:</span>
                    <span class="value link-text">{{ product.productName }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">数量:</span>
                    <span class="value">{{ product.quantity }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 如果没有商品数据 -->
            <div v-else class="no-products">
              <span>暂无商品信息</span>
            </div>
          </div>

          <!-- 底部按钮 -->
          <div class="bottom-actions">
            <!-- <a-button type="primary" size="large" block class="confirm-btn">
              已发货，点击查看发货详情
            </a-button> -->
             <a-button type="primary" size="large" block class="confirm-btn" @click="jump">
              去发货
            </a-button>
          </div>
        </div>
      </div>
    </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useRouter } from 'vue-router'
import supplyChainApi from '@/api/h5/supplyChain.js'

definePageMeta({
  layout: 'false',
  name: 'h5-SupplyChain-HavedShipped',
  path: '/h5/SupplyChain/HavedShipped/:id'
})

// 路由实例
const router = useRouter()
const route = useRoute()
const linkId = route.params.id // 这里是发货链接ID，不是订单ID

// 页面数据
const loading = ref(true)
const error = ref(null)
const orderData = ref({
  createTime: '',
  orderNo: '',
  receiverName: '',
  receiverAddress: '',
  receiverPhone: '',
  productId: '', // 产品ID，用于跳转
  productCategory: '',
  productCode: '',
  productName: '',
  quantity: ''
})

// 完整的发货数据
const shippingData = ref(null)
const products = ref([])

// 获取发货页面数据
const fetchShippingData = async () => {
  try {
    loading.value = true
    error.value = null
    console.log('开始获取发货数据，链接ID:', linkId)
    console.log('supplyChainApi:', supplyChainApi)
    console.log('getShippingPageData方法:', supplyChainApi.getShippingPageData)

    const response = await supplyChainApi.getShippingPageData(linkId)
    console.log('API响应:', response)

    if (response && response.code === 200) {
      shippingData.value = response.data

      // 设置订单基本信息
      orderData.value = {
        createTime: formatDate(response.data.orderInfo?.createTime),
        orderNo: response.data.orderInfo?.orderNumber || response.data.purchaseOrderNumber,
        receiverName: response.data.receiverName,
        receiverAddress: response.data.receiverAddress,
        receiverPhone: response.data.receiverPhone,
        // 如果有多个商品，显示第一个商品的信息
        productId: response.data.products?.[0]?.productId || '',
        productCategory: response.data.products?.[0]?.productCategory || '',
        productCode: response.data.products?.[0]?.productCode || '',
        productName: response.data.products?.[0]?.productName || '',
        quantity: response.data.products?.[0]?.quantity || ''
      }

      products.value = response.data.products || []
      console.log('数据设置完成:', { orderData: orderData.value, products: products.value })
    } else {
      const errorMsg = response?.message || '获取发货数据失败'
      console.error('获取发货数据失败:', errorMsg)
      error.value = errorMsg
    }
  } catch (err) {
    console.error('获取发货数据失败:', err)
    error.value = err.message || '网络请求失败，请检查网络连接'
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (dateStr) => {
  return dateStr || new Date().toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).replace(/\//g, '-')
}

const jump = () => {
  router.push({ path: `/h5/SupplyChain/SendGoods`})
}

// 跳转到产品详情页面
const goToProductDetails = (product) => {
  const productId = product?.productId || orderData.value.productId
  if (productId) {
    router.push({ path: `/h5/SupplyChain/ProductDetails/${productId}` })
  } else {
    console.warn('商品ID不存在，无法跳转到详情页面')
  }
}

onMounted(() => {
  // 根据链接ID获取发货数据
  console.log('发货链接ID:', linkId)
  fetchShippingData()
})
</script>
<style lang="less">
.haved-shipped-page {
  background: #fff;
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  position: relative;
  overflow-x: hidden;
  touch-action: manipulation;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;

  // 固定头部样式
  .fixed-header {
    z-index: 100;

    .date-header {
      background: #fff;
      padding: 12px 16px;
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      color: #333;
      border-bottom: 1px solid #e8e8e8;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  // 页面内容
  .content {
    background: #fff;
    padding: 16px;
    // 订单号
    .order-number {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 24px;
      text-align: center;
    }

    // 区块样式
    .section {
      margin-bottom: 32px;

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0 0 16px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;
      }
    }

    // 信息项样式
    .info-item {
      display: flex;
      margin-bottom: 12px;
      line-height: 1.6;

      .label {
        color: #666;
        font-size: 14px;
        min-width: 80px;
        flex-shrink: 0;
      }

      .value {
        color: #333;
        font-size: 14px;
        flex: 1;
        word-break: break-all;
      }

      &.clickable {
        cursor: pointer;
        padding: 8px;
        margin: -8px;
        border-radius: 4px;
        transition: all 0.2s;

        &:hover {
          background-color: #f5f5f5;

          .value.link-text {
            color: #1890ff;
          }
        }

        .value.link-text {
          color: inherit;
          text-decoration: none;
          transition: color 0.2s;
        }
      }

      // 商品名称特殊样式
      &.product-name {
        align-items: flex-start;

        .value {
          line-height: 1.5;
        }
      }
    }

    // 底部按钮
    .bottom-actions {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 0;
      background: #1989FA;
      z-index: 99;

      .confirm-btn {
        height: 60px;
        font-size: 16px;
        font-weight: 500;
        border-radius: 0;
        background: #1989FA;
        border: none;
        color: #fff;
        width: 100%;

        &:hover {
          background: #0F7AE5;
        }

        &:focus {
          background: #1989FA;
        }
      }
    }
  }
}

// 响应式设计
@media (min-width: 500px) {
  .haved-shipped-page {
    margin: 0 auto;
    max-width: 500px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    position: relative;

    .content .bottom-actions {
      left: 50%;
      transform: translateX(-50%);
      max-width: 500px;
      width: 500px;
    }
  }
}

// 为内容区域添加底部间距，避免被固定按钮遮挡
.content {
  padding-bottom: 80px !important;
}

// 加载状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;

  .loading-text {
    margin-top: 16px;
    color: #666;
    font-size: 14px;
  }
}

// 错误状态
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;

  .error-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .error-text {
    color: #f53f3f;
    font-size: 14px;
    text-align: center;
    margin-bottom: 20px;
    line-height: 1.5;
  }
}

// 多商品列表
.products-list {
  .product-item {
    margin-bottom: 20px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    overflow: hidden;

    &:last-child {
      margin-bottom: 0;
    }

    .product-header {
      background: #f5f5f5;
      padding: 8px 16px;
      border-bottom: 1px solid #e8e8e8;

      .product-index {
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }
    }

    .product-info {
      padding: 16px;
    }
  }
}

// 无商品状态
.no-products {
  text-align: center;
  padding: 40px 20px;
  color: #999;
  font-size: 14px;
}
</style>