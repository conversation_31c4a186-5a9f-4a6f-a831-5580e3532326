.order-table {

    border-radius: 8px;
}

/* 订单信息样式 */
.order-info-card {
    padding: 8px;

    border-radius: 4px;
    font-size: 12px;
}

.info-row {
    display: flex;
    margin-bottom: 4px;
    line-height: 1.4;
}

.info-row:last-child {
    margin-bottom: 0;
}

.label {
    font-weight: 500;
    color: #666;
    min-width: 70px;
    flex-shrink: 0;
}

/* 订单编号和下单账号样式 */
.order-number {
    font-weight: 600;
    color: #165dff;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.order-account {
    font-weight: 500;
    color: #333;
}

/* 产品线过期时间显示样式 */
.expire-time-display {
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: inline-block;
}

.expire-time-display.expired {
    color: #ffffff;
    background-color: #f53f3f;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(245, 63, 63, 0.3);
    animation: pulse-red 2s infinite;
}

.expire-time-display.expiring {
    color: #ffffff;
    background-color: #ff7d00;
    font-weight: 600;
}

.expire-time-display.valid {
    color: #00b42a;
    background-color: rgba(0, 180, 42, 0.1);
}

/* 过期时间闪烁动画 */
@keyframes pulse-red {
    0% {
        box-shadow: 0 2px 4px rgba(245, 63, 63, 0.3);
    }
    50% {
        box-shadow: 0 2px 8px rgba(245, 63, 63, 0.6);
        transform: scale(1.02);
    }
    100% {
        box-shadow: 0 2px 4px rgba(245, 63, 63, 0.3);
    }
}

/* 产品线过期行样式 */
.order-table :deep(.arco-table-tr.expired-row) {
    background-color: rgba(245, 63, 63, 0.1) !important;
}

.order-table :deep(.arco-table-tr.expired-row:hover) {
    background-color: rgba(245, 63, 63, 0.15) !important;
}

.order-table :deep(.arco-table-tr.expired-row .arco-table-td) {
    border-bottom-color: rgba(245, 63, 63, 0.2);
}

/* 提示文本样式 */
.supplier-note,
.cost-note {
    padding: 8px;
    text-align: center;
    background: #f8f9fa;
    border-radius: 4px;
    margin: 8px 0;
}

.note-text {
    font-size: 12px;
    color: #86909c;
    font-style: italic;
}

/* 商品信息样式 */
.product-info-card {
    padding: 8px;
    border-radius: 4px;
}

/* 多商品容器样式 */
.products-container {
    width: 100%;
}

.products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding: 4px 8px;
    background: #f0f2f5;
    border-radius: 4px;
    font-size: 12px;
}

.products-count {
    color: #165dff;
    font-weight: 500;
}

.total-quantity {
    color: #666;
}

.products-list {
    max-height: 200px;
    overflow-y: auto;
}

.product-item {
    display: flex;
    gap: 8px;
    padding: 6px 0;
    align-items: flex-start;
}

.product-item.has-border {
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 6px;
    padding-bottom: 10px;
}

.product-image {
    flex-shrink: 0;
}

.product-details {
    flex: 1;
    font-size: 11px;
    min-width: 0;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 2px;
}

.product-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 2px;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.product-sku,
.product-code,
.product-spec {
    color: #666;
    margin-bottom: 1px;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.product-price-qty {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-top: 4px;
    font-size: 11px;
}

.product-price-qty .price {
    color: #f53f3f;
    font-weight: 500;
}

.product-price-qty .quantity {
    color: #165dff;
}

.product-price-qty .subtotal {
    color: #333;
    font-weight: 500;
}

/* 商品成本信息样式 */
.product-cost-info {
    margin-top: 4px;
    font-size: 11px;
}

.cost-info-text {
    color: #666;
    font-weight: 500;
}

/* 商品成本总价样式 */
.product-cost-total {
    margin-top: 2px;
    font-size: 11px;
    display: flex;
    align-items: center;
}

.cost-total-label {
    color: #666;
    margin-right: 4px;
}

.cost-total-value {
    color: #f53f3f;
    font-weight: 600;
}

/* 商品供应商信息样式 */
.product-supplier-info {
    margin-top: 4px;
    font-size: 10px;
}

.product-supplier-info .supplier-row {
    display: flex;
    align-items: center;
    margin-bottom: 2px;
    line-height: 1.2;
}

.product-supplier-info .supplier-row:last-child {
    margin-bottom: 0;
}

.product-supplier-info .supplier-label {
    font-weight: 500;
    color: #666;
    min-width: 30px;
    flex-shrink: 0;
    font-size: 10px;
}

.product-supplier-info .supplier-link {
    color: #165dff;
    font-size: 10px;
    padding: 0 2px;
    height: auto;
    line-height: 1.2;
    min-height: auto;
}

.product-supplier-info .supplier-link :deep(.arco-btn-icon) {
    font-size: 8px;
    margin-left: 2px;
}

.product-supplier-info .supplier-text {
    color: #333;
    font-size: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 单商品样式（兼容旧数据） */
.single-product {
    display: flex;
    gap: 12px;
}

.single-product .product-details {
    font-size: 12px;
}

.single-product .product-name {
    margin-bottom: 4px;
}

.single-product .product-sku,
.single-product .product-code,
.single-product .product-spec,
.single-product .tax-category {
    margin-bottom: 2px;
    line-height: 1.3;
}

/* 采购成本信息样式 */
.cost-info-card {
    padding: 8px;
    border-radius: 4px;
    font-size: 12px;
}

.products-cost-detail,
.single-product-cost {
    margin-top: 12px;
    padding-top: 8px;
}

.product-cost-item {
    margin-bottom: 8px;
    padding: 6px;
    background: #fafafa;
    border-radius: 4px;
}

.product-cost-item:last-child {
    margin-bottom: 0;
}

.product-cost-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
    font-size: 11px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.product-cost-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
    font-size: 10px;
}

.cost-detail-item {
    color: #666;
    line-height: 1.4;
}

.cost-detail-item.profit {
    color: #00b42a;
    font-weight: 500;
}

.total-amount {
    color: #f53f3f;
    font-weight: 600;
}

.cost-item {
    margin-bottom: 4px;
    line-height: 1.4;
}

.cost-item:last-child {
    margin-bottom: 0;
}

.cost-label {
    font-weight: 500;
    color: #666;
    display: inline-block;
    min-width: 90px;
}

.cost-sub-label {
    font-size: 11px;
    color: #999;
    margin-left: 90px;
}

.cost-content {
    color: #333;
    word-break: break-word;
    line-height: 1.4;
}

.cost-content.profit {
    color: #52c41a;
    font-weight: 500;
}

/* 采购信息样式 */
.purchase-info-card {
    padding: 8px;

    border-radius: 4px;
    font-size: 12px;
}

.purchase-item {
    margin-bottom: 6px;
    line-height: 1.4;
}

.purchase-item:last-child {
    margin-bottom: 0;
}

.purchase-label {
    font-weight: 500;
    color: #666;
    display: inline-block;
    margin-bottom: 2px;
    min-width: 80px;
}

.purchase-content {
    color: #333;
    word-break: break-word;
    line-height: 1.4;
}

/* 采购进度选择器样式 */
.purchase-progress-wrapper {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
}

.purchase-progress-select {
    width: 100%;
    font-size: 12px;
}

.purchase-progress-select :deep(.arco-select-view-single) {
    font-size: 12px;
    height: 24px;
    line-height: 22px;
}

.purchase-progress-select :deep(.arco-select-view-suffix) {
    line-height: 22px;
}

/* 货期时间输入框样式 */
.delivery-time-wrapper {
    margin-top: 4px;
}

.delivery-time-input {
    width: 100%;
    font-size: 12px;
}

.delivery-time-input :deep(.arco-input) {
    font-size: 12px;
    height: 24px;
    line-height: 22px;
    border: 1px solid #e5e6eb;
    border-radius: 4px;
}

.delivery-time-input :deep(.arco-input:hover) {
    border-color: #165dff;
}

.delivery-time-input :deep(.arco-input:focus) {
    border-color: #165dff;
    box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.1);
}

/* 操作按钮样式 */
.generate-link-btn {
    color: #52c41a !important;
}

.generate-link-btn:hover {
    color: #389e0d !important;
    background-color: rgba(82, 196, 26, 0.1) !important;
}

.copy-link-btn {
    color: #165dff !important;
}

/* 废止操作弹窗样式 */
.abolish-operation-content {
    padding: 16px 0;
}

.order-info {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 16px;
}

.info-item {
    display: flex;
    margin-bottom: 8px;
    font-size: 14px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    font-weight: 500;
    color: #666;
    min-width: 80px;
    flex-shrink: 0;
}

.info-value {
    color: #333;
    font-weight: 500;
}

.form-section {
    margin-bottom: 16px;
}

.form-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
    font-size: 14px;
}

.form-item:last-child {
    margin-bottom: 0;
}

.form-label {
    font-weight: 500;
    color: #333;
    min-width: 80px;
    flex-shrink: 0;
    line-height: 32px;
}

.form-label.required::before {
    content: '*';
    color: #f53f3f;
    margin-right: 4px;
}

.radio-group {
    display: flex;
    gap: 16px;
}

.agree-radio :deep(.arco-radio-label) {
    color: #00b42a;
    font-weight: 500;
}

.disagree-radio :deep(.arco-radio-label) {
    color: #f53f3f;
    font-weight: 500;
}

.remark-textarea {
    flex: 1;
    margin-left: 12px;
}

.warning-tip {
    display: flex;
    align-items: center;
    padding: 12px;
    background: #fff7e6;
    border: 1px solid #ffd591;
    border-radius: 6px;
    margin-bottom: 20px;
}

.warning-icon {
    color: #fa8c16;
    font-size: 16px;
    margin-right: 8px;
    flex-shrink: 0;
}

.warning-text {
    color: #d46b08;
    font-size: 14px;
}

.button-group {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.cancel-btn {
    min-width: 80px;
}

.confirm-btn {
    min-width: 80px;
}

/* 废止记录弹窗样式 */
.abolish-record-content {
    padding: 8px 0;
}

.copy-link-btn:hover {
    color: #0e42d2 !important;
    background-color: rgba(22, 93, 255, 0.1) !important;
}

/* 生成链接弹窗样式 */
.generate-link-modal :deep(.arco-modal-header) {
    padding: 16px 24px;
    border-bottom: 1px solid #e5e6eb;
}

.generate-link-modal :deep(.arco-modal-title) {
    font-size: 16px;
    font-weight: 500;
    color: #1d2129;
}

.generate-link-modal :deep(.arco-modal-body) {
    padding: 24px;
}

.generate-link-content {
    width: 100%;
}

.confirm-question {
    font-size: 16px;
    color: #1d2129;
    margin-bottom: 32px;
    line-height: 1.5;
}

.form-container {
    margin-bottom: 40px;
}

.form-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
}

.form-row:last-child {
    margin-bottom: 0;
}

.form-label {
    display: flex;
    align-items: center;
    width: 120px;
    font-size: 14px;
    color: #1d2129;
    line-height: 32px;
    flex-shrink: 0;
    margin-right: 12px;
}

.required-star {
    color: #f53f3f;
    margin-right: 4px;
    font-size: 14px;
}

.form-control {
    flex: 1;
    min-width: 0;
}

.form-control :deep(.arco-input) {
    height: 32px;
    border-radius: 4px;
    font-size: 14px;
    color: #1d2129;
}

.form-control :deep(.arco-input:focus) {
}

.form-control :deep(.arco-input::placeholder) {
    color: #c9cdd4;
}

.form-textarea :deep(.arco-textarea) {
    min-height: 80px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    color: #1d2129;
    resize: vertical;
}

.form-textarea :deep(.arco-textarea:focus) {
    border-color: #165dff;
    box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.1);
}

.form-textarea :deep(.arco-textarea::placeholder) {
    color: #c9cdd4;
}

.button-group {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 24px;
}

.cancel-btn {
    min-width: 80px;
    height: 36px;
    border: 1px solid #d9d9d9;
    background: #ffffff;
    color: #1d2129;
    border-radius: 4px;
    font-size: 14px;
}

.cancel-btn:hover {
    border-color: #165dff;
    color: #165dff;
}

.confirm-btn {
    min-width: 80px;
    height: 36px;
    background: #f53f3f;
    border: 1px solid #f53f3f;
    color: #ffffff;
    border-radius: 4px;
    font-size: 14px;
}

.confirm-btn:hover {
    background: #e63946;
    border-color: #e63946;
}

.confirm-btn:disabled {
    background: #f7f8fa;
    border-color: #e5e6eb;
    color: #c9cdd4;
    cursor: not-allowed;
}

/* 复制链接弹窗样式 */
.copy-link-content {
    padding: 16px 0;
}

.order-info-section {
    margin-bottom: 16px;
}

.info-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
}

.info-label {
    font-weight: 500;
    color: #333;
    min-width: 80px;
}

.info-value {
    color: #666;
}

.qrcode-section {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
}

.qrcode-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.qrcode-placeholder {
    width: 150px;
    height: 150px;
    border: 1px solid #e5e6eb;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
}

.qrcode-mock {
    width: 140px;
    height: 140px;
}

.qrcode-grid {
    display: grid;
    grid-template-columns: repeat(15, 1fr);
    gap: 1px;
    width: 100%;
    height: 100%;
}

.qrcode-dot {
    background: white;
    border-radius: 1px;
}

.qrcode-dot.active {
    background: #333;
}

.qrcode-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.qrcode-order-number {
    font-size: 12px;
    color: #666;
    text-align: center;
}

.copy-order-btn {
    font-size: 12px;
    height: 24px;
    padding: 0 12px;
}

.order-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.detail-item {
    display: flex;
    font-size: 12px;
    line-height: 1.5;
}

.detail-label {
    font-weight: 500;
    color: #666;
    min-width: 70px;
    flex-shrink: 0;
}

.detail-value {
    color: #333;
    word-break: break-word;
}

.link-section {
    margin-bottom: 20px;
}

.link-label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.link-input-wrapper {
    display: flex;
    gap: 8px;
    align-items: center;
}

.link-input {
    flex: 1;
}

.link-input :deep(.arco-input) {
    font-size: 12px;
    color: #165dff;
    background: #f8f9fa;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    padding-top: 16px;
    border-top: 1px solid #e5e6eb;
}

/* 备注信息样式 */
.remark-info-card {
    padding: 8px;

    border-radius: 4px;
    font-size: 12px;
}

.remark-item {
    margin-bottom: 6px;
    line-height: 1.4;
}

.remark-item:last-child {
    margin-bottom: 0;
}

.remark-label {
    font-weight: 500;
    color: #666;
    display: inline-block;
    margin-bottom: 2px;
}

.remark-content {
    color: #333;
    word-break: break-word;
    line-height: 1.4;
}

.text-gray {
    color: #999;
    font-style: italic;
}

/* 供应商信息样式 */
.supplier-info {
    font-size: 12px;
    text-align: left;
    padding: 4px;
}

.supplier-row {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    line-height: 1.4;
}

.supplier-row:last-child {
    margin-bottom: 0;
}

.supplier-label {
    font-weight: 500;
    color: #666;
    min-width: 40px;
    flex-shrink: 0;
}

.supplier-link {
    color: #165dff;
    padding: 0 4px;
    font-size: 12px;
    height: auto;
    min-height: auto;
}

.supplier-link:hover {
    color: #0e42d2;
    background: rgba(22, 93, 255, 0.1);
}

.supplier-text {
    color: #333;
}

/* 税收分类样式 */
.product-tax-category {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    font-size: 12px;
}

.tax-label {
    font-weight: 500;
    color: #666;
    min-width: 70px;
    flex-shrink: 0;
}

.tax-link {
    color: #165dff;
    padding: 0 4px;
    font-size: 12px;
    height: auto;
    min-height: auto;
}

.tax-link:hover {
    color: #0e42d2;
    background: rgba(22, 93, 255, 0.1);
}

/* 亏损信息样式 */
.loss-info {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #f0f0f0;
}

.loss-amount {
    margin-bottom: 8px;
}

.loss-text {
    color: #f53f3f;
    font-weight: 500;
}

.loss-reason-section {
    margin-top: 4px;
}

.loss-reason-select {
    width: 100%;
    font-size: 12px;
}

/* 成本输入框样式 */
.cost-input-wrapper {
    display: flex;
    align-items: center;
    flex: 1;
}

.currency-symbol {
    color: #666;
    margin-right: 4px;
    font-size: 12px;
}

.cost-input {
    flex: 1;
    font-size: 12px;
}

.cost-input :deep(.arco-input) {
    border: none;
    border-radius: 4px;
    padding: 2px 8px;
    height: 24px;
    line-height: 20px;
    background: transparent;
}

.cost-input :deep(.arco-input:hover) {
    border: none;
    background: rgba(22, 93, 255, 0.05);
}

.cost-input :deep(.arco-input:focus) {
    border: none;
    box-shadow: none;
    background: rgba(22, 93, 255, 0.05);
}

/* 状态信息样式 */
.status-info-card {
    padding: 8px;

    border-radius: 4px;
    font-size: 12px;
}

.status-item {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    line-height: 1.4;
}

.status-item:last-child {
    margin-bottom: 0;
}

.status-label {
    font-weight: 500;
    color: #666;
    min-width: 70px;
    flex-shrink: 0;
    margin-right: 8px;
}

/* 订单编号和下单账号样式 */
.order-number {
    font-weight: 600;
    color: #165dff;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.order-account {
    font-weight: 500;
    color: #333;
}

/* 产品线过期行样式 */
.ma-crud :deep(.arco-table-tr.expired-row) {
    background-color: rgba(245, 63, 63, 0.1) !important;
}

.ma-crud :deep(.arco-table-tr.expired-row:hover) {
    background-color: rgba(245, 63, 63, 0.15) !important;
}

.ma-crud :deep(.arco-table-tr.expired-row .arco-table-td) {
    border-bottom-color: rgba(245, 63, 63, 0.2);
}


.clickable-image {
    cursor: pointer;
    transition: all 0.2s;
}

.clickable-image:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}