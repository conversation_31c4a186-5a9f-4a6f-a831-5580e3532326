/**
 * 采购订单控制器
 * 负责处理采购订单相关的请求和响应
 */
const BaseController = require("../../../../core/controllers/BaseController");
const PurchaseOrderService = require("../services/PurchaseOrderService");
const QRCode = require('qrcode');
const IntegrationFactory = require("../../system/integration/common/IntegrationFactory");

class PurchaseOrderController extends BaseController {
  constructor(prisma) {
    super();
    this.prisma = prisma;
    this.purchaseOrderService = new PurchaseOrderService(prisma);
    this.integrationFactory = new IntegrationFactory(prisma);
  }

  /**
   * 根据原始订单号获取采购员信息和商品信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async getPurchaserByOrderNumber(req, res) {
    try {
      const { orderNumber } = req.params;

      if (!orderNumber) {
        return this.fail(res, "订单号不能为空", 400);
      }

      // 调用服务层获取采购员信息和商品信息
      const orderInfo =
        await this.purchaseOrderService.getPurchaserByOrderNumber(orderNumber);

      if (!orderInfo) {
        return this.fail(res, "未找到该订单的采购员信息", 404);
      }

      return this.success(res, orderInfo, "获取订单信息成功");
    } catch (error) {
      console.error("获取订单信息失败:", error);
      return this.fail(res, error.message || "获取订单信息失败", 500);
    }
  }

  /**
   * 根据采购订单号获取采购员信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async getPurchaserByPurchaseOrderNumber(req, res) {
    try {
      const { purchaseOrderNumber } = req.params;

      if (!purchaseOrderNumber) {
        return this.fail(res, "采购订单号不能为空", 400);
      }

      // 调用服务层获取采购员信息
      const purchaserInfo =
        await this.purchaseOrderService.getPurchaserByPurchaseOrderNumber(
          purchaseOrderNumber
        );

      if (!purchaserInfo) {
        return this.fail(res, "未找到该采购订单的采购员信息", 404);
      }

      return this.success(res, purchaserInfo, "获取采购员信息成功");
    } catch (error) {
      console.error("根据采购订单号获取采购员信息失败:", error);
      return this.fail(res, error.message || "获取采购员信息失败", 500);
    }
  }

  /**
   * 申请采购订单
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async applyPurchaseOrder(req, res) {
    try {
      // 获取当前用户信息
      const currentUser = req.user || {
        id: 1,
        nickname: "系统用户",
        username: "system",
      };

      // 验证请求数据
      const {
        orderId,
        purchaserId,
        sourceSupplier,
        actualReceiver,
        contactPhone,
        actualAddress,
      } = req.body;

      if (!orderId) {
        return this.fail(res, "订单ID不能为空", 400);
      }

      if (!purchaserId) {
        return this.fail(res, "采购员ID不能为空", 400);
      }

      if (!sourceSupplier) {
        return this.fail(res, "采购员不能为空", 400);
      }

      if (!actualReceiver) {
        return this.fail(res, "实际收货人不能为空", 400);
      }

      if (!contactPhone) {
        return this.fail(res, "联系电话不能为空", 400);
      }

      if (!actualAddress) {
        return this.fail(res, "实际收货地址不能为空", 400);
      }

      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(contactPhone)) {
        return this.fail(res, "请输入正确的手机号码", 400);
      }

      // 记录采购员信息
      console.log("申请采购订单 - 采购员信息:", {
        purchaserId,
        sourceSupplier,
        orderId,
      });

      // 调用服务申请采购订单
      const result = await this.purchaseOrderService.applyPurchaseOrder(
        req.body,
        currentUser
      );

      // 返回成功响应
      this.success(res, result.data, result.message, 200);
    } catch (error) {
      // 记录错误日志
      console.error("申请采购订单失败", error);

      // 根据错误类型返回不同的响应
      if (error.message.includes("不存在")) {
        return this.fail(res, error.message, 404);
      }
      if (error.message.includes("已经申请过")) {
        return this.fail(res, error.message, 409);
      }

      // 返回通用错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取采购订单列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async getPurchaseOrders(req, res) {
    try {
      // 获取查询参数
      const page = parseInt(req.query.page) || 1;
      const pageSize = parseInt(req.query.pageSize) || 10;

      // 构建过滤条件
      const filters = {};

      // 调试日志：打印请求参数
      console.log("采购订单列表请求参数:", req.query);

      // 基础筛选条件
      if (req.query.purchaseOrderNumber) {
        filters.purchase_order_number = {
          contains: req.query.purchaseOrderNumber,
        };
      }

      if (req.query.originalOrderId) {
        filters.original_order_id = parseInt(req.query.originalOrderId);
      }

      if (req.query.originalOrderNumber) {
        filters.original_order_number = {
          contains: req.query.originalOrderNumber,
        };
      }

      // 采购相关筛选
      if (
        req.query.purchaseStatus !== undefined &&
        req.query.purchaseStatus !== ""
      ) {
        filters.purchase_status = parseInt(req.query.purchaseStatus);
      }

      if (req.query.purchaser) {
        filters.purchaser_name = { contains: req.query.purchaser };
      }

      if (req.query.follower) {
        filters.follower = { contains: req.query.follower };
      }

      // 订单基础信息筛选
      if (req.query.orderSource) {
        filters.order_source = req.query.orderSource;
      }

      if (req.query.orderType) {
        filters.order_type = req.query.orderType;
      }

      if (req.query.buyerAccount) {
        filters.buyer_account = { contains: req.query.buyerAccount };
      }

      // 渠道ID筛选
      if (req.query.channelId) {
        try {
          filters.channel_id = BigInt(req.query.channelId);
        } catch (error) {
          console.error("渠道ID格式无效:", req.query.channelId, error.message);
          // 如果转换失败，设置一个不可能存在的ID，确保查询返回空结果
          filters.channel_id = BigInt(0);
        }
      }

      // 状态筛选
      if (req.query.orderStatus) {
        filters.order_status = this.convertOrderStatusToInt(
          req.query.orderStatus
        );
      }

      if (req.query.erpStatus) {
        filters.erp_status = this.convertErpStatusToInt(req.query.erpStatus);
      }

      if (req.query.auditStatus) {
        filters.audit_status = this.convertAuditStatusToInt(
          req.query.auditStatus
        );
      }

      if (req.query.linkStatus) {
        filters.link_status = this.convertLinkStatusToInt(req.query.linkStatus);
      }

      if (req.query.isLoss) {
        filters.is_loss = this.convertIsLossToInt(req.query.isLoss);
      }

      // 供应商筛选
      if (req.query.supplierName) {
        filters.supplier_name = { contains: req.query.supplierName };
      }

      // 物流信息筛选
      if (req.query.logisticsNumber) {
        filters.logistics_number = { contains: req.query.logisticsNumber };
      }

      if (req.query.orderAddress) {
        filters.delivery_address = { contains: req.query.orderAddress };
      }

      if (req.query.purchaseRemark) {
        filters.purchase_remark = { contains: req.query.purchaseRemark };
      }

      // 商品相关筛选
      if (req.query.productCode) {
        filters.productCode = req.query.productCode;
      }

      if (req.query.productName) {
        filters.productName = req.query.productName;
      }

      // 收货人信息筛选
      if (req.query.recipientName) {
        filters.recipient_name = { contains: req.query.recipientName };
      }

      if (req.query.contactPhone) {
        filters.contactPhone = req.query.contactPhone;
      }

      // 成本价范围筛选
      if (req.query["costPrice[0]"] || req.query["costPrice[1]"]) {
        const minCost = req.query["costPrice[0]"];
        const maxCost = req.query["costPrice[1]"];
        if (minCost || maxCost) {
          filters.costPrice = [minCost, maxCost];
        }
      } else if (req.query.costPrice) {
        filters.costPrice = req.query.costPrice;
      }

      // 时间范围筛选
      if (
        req.query.purchaseTimeRange &&
        Array.isArray(req.query.purchaseTimeRange) &&
        req.query.purchaseTimeRange.length === 2
      ) {
        filters.purchase_time = {
          gte: new Date(req.query.purchaseTimeRange[0]),
          lte: new Date(req.query.purchaseTimeRange[1]),
        };
      }

      if (
        req.query.orderTimeRange &&
        Array.isArray(req.query.orderTimeRange) &&
        req.query.orderTimeRange.length === 2
      ) {
        filters.original_order_time = {
          gte: new Date(req.query.orderTimeRange[0]),
          lte: new Date(req.query.orderTimeRange[1]),
        };
      }

      if (req.query.startTime && req.query.endTime) {
        filters.purchase_apply_time = {
          gte: new Date(parseInt(req.query.startTime)),
          lte: new Date(parseInt(req.query.endTime)),
        };
      }

      // 调用服务获取采购订单列表
      const result = await this.purchaseOrderService.getPurchaseOrders(
        filters,
        page,
        pageSize
      );

      // 返回成功响应
      this.success(
        res,
        {
          items: result.items,
          pageInfo: {
            total: result.total,
            currentPage: result.page,
            pageSize: result.pageSize,
            totalPage: result.totalPages,
          },
        },
        "获取采购订单列表成功"
      );
    } catch (error) {
      // 记录错误日志
      console.error("获取采购订单列表失败", error);

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取采购订单详情
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async getPurchaseOrderDetail(req, res) {
    try {
      const { id } = req.params;

      if (!id) {
        return this.fail(res, "采购订单ID不能为空", 400);
      }

      // 调用服务获取采购订单详情
      const result = await this.purchaseOrderService.getPurchaseOrderById(id);

      // 返回成功响应
      this.success(res, result, "获取采购订单详情成功");
    } catch (error) {
      // 记录错误日志
      console.error("获取采购订单详情失败", error);

      // 根据错误类型返回不同的响应
      if (error.message.includes("不存在")) {
        return this.fail(res, error.message, 404);
      }

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取采购订单详情（新版本）- 分离采购订单信息、原订单信息和关联数据
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async getPurchaseOrderDetailV2(req, res) {
    try {
      const { id } = req.params;

      if (!id) {
        return this.fail(res, "采购订单ID不能为空", 400);
      }

      // 调用服务获取分离格式的采购订单详情
      const result =
        await this.purchaseOrderService.getPurchaseOrderDetailSeparated(id);

      // 返回成功响应
      this.success(res, result, "获取采购订单详情成功");
    } catch (error) {
      // 记录错误日志
      console.error("获取采购订单详情失败", error);

      // 根据错误类型返回不同的响应
      if (error.message.includes("不存在")) {
        return this.fail(res, error.message, 404);
      }

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 更新采购订单状态
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async updatePurchaseOrderStatus(req, res) {
    try {
      const { id } = req.params;
      const { purchaseStatus, purchaserName, supplierName, remark } = req.body;
      const currentUser = req.user || {
        id: 1,
        nickname: "系统用户",
        username: "system",
      };

      if (!id) {
        return this.fail(res, "采购订单ID不能为空", 400);
      }

      if (purchaseStatus === undefined) {
        return this.fail(res, "采购状态不能为空", 400);
      }

      // 更新采购订单
      const updatedOrder = await this.prisma.purchase_order.update({
        where: { id: parseInt(id) },
        data: {
          purchase_status: purchaseStatus,
          purchaser_name: purchaserName,
          supplier_name: supplierName,
          purchase_remark: remark,
          updater: currentUser.nickname || currentUser.username,
          updated_at: new Date(),
        },
      });

      // 记录日志
      await this.prisma.purchase_order_log.create({
        data: {
          purchase_order_id: parseInt(id),
          action_type: "update",
          action_description: "更新采购订单状态",
          old_status: null, // 这里可以先查询原状态
          new_status: purchaseStatus,
          status_description: this.getStatusDescription(purchaseStatus),
          operator_id: currentUser.id,
          operator_name: currentUser.nickname || currentUser.username,
          operator_role: currentUser.role || "user",
          details: {
            purchaserName,
            supplierName,
            remark,
          },
          remark: "手动更新采购订单状态",
        },
      });

      // 返回成功响应
      this.success(res, updatedOrder, "更新采购订单状态成功");
    } catch (error) {
      // 记录错误日志
      console.error("更新采购订单状态失败", error);

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取状态描述
   * @param {number} status - 状态值
   * @returns {string} - 状态描述
   */
  getStatusDescription(status) {
    const statusMap = {
      0: "待处理",
      1: "已分配",
      2: "采购中",
      3: "已完成",
      4: "已取消",
    };
    return statusMap[status] || "未知状态";
  }

  /**
   * 状态转换方法
   */
  convertOrderStatusToInt(status) {
    // 如果已经是数字，直接返回
    if (typeof status === "number" || !isNaN(parseInt(status))) {
      return parseInt(status);
    }

    const statusMap = {
      待发货: 0,
      发货未完成: 1,
      待收货: 2,
      已废止: 3,
      已关闭: 4,
      废止待确认: 5,
    };
    return statusMap[status] !== undefined
      ? statusMap[status]
      : parseInt(status) || 0;
  }

  convertErpStatusToInt(status) {
    // 如果已经是数字，直接返回
    if (typeof status === "number" || !isNaN(parseInt(status))) {
      return parseInt(status);
    }

    const statusMap = {
      待同步: 0,
      已同步: 1,
      同步失败: 2,
    };
    return statusMap[status] !== undefined
      ? statusMap[status]
      : parseInt(status) || 0;
  }

  convertAuditStatusToInt(status) {
    // 如果已经是数字，直接返回
    if (typeof status === "number" || !isNaN(parseInt(status))) {
      return parseInt(status);
    }

    const statusMap = {
      待审核: 0,
      审核通过: 1,
      审核驳回: 2,
      废止待确认: 3,
    };
    return statusMap[status] !== undefined
      ? statusMap[status]
      : parseInt(status) || 0;
  }

  convertLinkStatusToInt(status) {
    // 如果已经是数字，直接返回
    if (typeof status === "number" || !isNaN(parseInt(status))) {
      return parseInt(status);
    }

    const statusMap = {
      unlinked: 0,
      linked: 1,
    };
    return statusMap[status] !== undefined
      ? statusMap[status]
      : parseInt(status) || 0;
  }

  convertIsLossToInt(status) {
    // 如果已经是数字，直接返回
    if (typeof status === "number" || !isNaN(parseInt(status))) {
      return parseInt(status);
    }

    const statusMap = {
      no: 0,
      yes: 1,
    };
    return statusMap[status] !== undefined
      ? statusMap[status]
      : parseInt(status) || 0;
  }

  /**
   * 更换采购员
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async changePurchaser(req, res) {
    try {
      const { id } = req.params;
      const { newPurchaserId, newPurchaser, changeReason } = req.body;
      const currentUser = req.user || {
        id: 1,
        nickname: "系统用户",
        username: "system",
      };

      if (!id) {
        return this.fail(res, "采购订单ID不能为空", 400);
      }

      if (!newPurchaserId) {
        return this.fail(res, "新采购员ID不能为空", 400);
      }

      if (!newPurchaser) {
        return this.fail(res, "新采购员不能为空", 400);
      }

      if (!changeReason) {
        return this.fail(res, "更换原因不能为空", 400);
      }

      // 查询当前订单信息
      const currentOrder = await this.prisma.purchase_order.findUnique({
        where: { id: parseInt(id) },
      });

      if (!currentOrder) {
        return this.fail(res, "采购订单不存在", 404);
      }

      // 检查新采购员是否与当前采购员相同（比较ID）
      if (
        currentOrder.purchaser_id &&
        currentOrder.purchaser_id.toString() === newPurchaserId.toString()
      ) {
        return this.fail(res, "新采购员不能与当前采购员相同", 400);
      }

      // 记录更换采购员信息
      console.log("更换采购员 - 采购员信息:", {
        原始purchaserId: currentOrder.purchaser_id,
        新purchaserId: newPurchaserId,
        新purchaser: newPurchaser,
      });

      // 更新采购员信息
      const updatedOrder = await this.prisma.purchase_order.update({
        where: { id: parseInt(id) },
        data: {
          purchaser_id: BigInt(newPurchaserId), // 更新采购员ID
          purchaser_name: newPurchaser, // 更新采购员username
          updater: currentUser.nickname || currentUser.username,
          updated_at: new Date(),
        },
      });

      // 记录操作日志
      await this.prisma.purchase_order_log.create({
        data: {
          purchase_order_id: parseInt(id),
          action_type: "change_purchaser",
          action_description: "更换采购员",
          old_status: null,
          new_status: null,
          status_description: `从 ${
            currentOrder.purchaser_name || "未分配"
          } 更换为 ${newPurchaser}`,
          operator_id: currentUser.id ? BigInt(currentUser.id) : null,
          operator_name: currentUser.nickname || currentUser.username,
          operator_role: currentUser.role || "user",
          details: {
            oldPurchaser: currentOrder.purchaser_name,
            newPurchaser: newPurchaser,
            changeReason: changeReason,
          },
          remark: changeReason,
        },
      });

      // 返回成功响应
      this.success(
        res,
        {
          id: updatedOrder.id,
          purchaser_name: updatedOrder.purchaser_name,
          updated_at: updatedOrder.updated_at,
        },
        "采购员更换成功"
      );
    } catch (error) {
      // 记录错误日志
      console.error("更换采购员失败", error);

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 更新采购备注
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async updatePurchaseRemark(req, res) {
    try {
      const { id } = req.params;
      const { newRemark, remarkType = "normal" } = req.body;
      const currentUser = req.user || {
        id: 1,
        nickname: "系统用户",
        username: "system",
      };

      if (!id) {
        return this.fail(res, "采购订单ID不能为空", 400);
      }

      if (!newRemark || newRemark.trim() === "") {
        return this.fail(res, "新增备注不能为空", 400);
      }

      // 查询当前订单信息
      const currentOrder = await this.prisma.purchase_order.findUnique({
        where: { id: parseInt(id) },
      });

      if (!currentOrder) {
        return this.fail(res, "采购订单不存在", 404);
      }

      // 构建新的备注内容（追加而不是覆盖）
      const timestamp = new Date().toLocaleString("zh-CN", {
        timeZone: "Asia/Shanghai",
      });
      const remarkTypeMap = {
        normal: "普通",
        urgent: "紧急",
        warning: "警告",
        quality: "质量",
        logistics: "物流",
      };
      const typeLabel = remarkTypeMap[remarkType] || "普通";

      const newRemarkEntry = `[${timestamp}] [${typeLabel}] ${
        currentUser.nickname || currentUser.username
      }: ${newRemark.trim()}`;

      let updatedRemark;
      if (
        currentOrder.purchase_remark &&
        currentOrder.purchase_remark.trim() !== ""
      ) {
        updatedRemark = `${currentOrder.purchase_remark}\n${newRemarkEntry}`;
      } else {
        updatedRemark = newRemarkEntry;
      }

      // 更新采购备注
      const updatedOrder = await this.prisma.purchase_order.update({
        where: { id: parseInt(id) },
        data: {
          purchase_remark: updatedRemark,
          updater: currentUser.nickname || currentUser.username,
          updated_at: new Date(),
        },
      });

      // 记录操作日志
      await this.prisma.purchase_order_log.create({
        data: {
          purchase_order_id: parseInt(id),
          action_type: "update_remark",
          action_description: "更新采购备注",
          old_status: null,
          new_status: null,
          status_description: `添加${typeLabel}备注`,
          operator_id: currentUser.id ? BigInt(currentUser.id) : null,
          operator_name: currentUser.nickname || currentUser.username,
          operator_role: currentUser.role || "user",
          details: {
            remarkType: remarkType,
            newRemark: newRemark,
            oldRemark: currentOrder.purchase_remark || "",
          },
          remark: `添加备注: ${newRemark}`,
        },
      });

      // 返回成功响应
      this.success(
        res,
        {
          id: updatedOrder.id,
          purchase_remark: updatedOrder.purchase_remark,
          updated_at: updatedOrder.updated_at,
        },
        "采购备注更新成功"
      );
    } catch (error) {
      // 记录错误日志
      console.error("更新采购备注失败", error);

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 更新采购进度
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async updatePurchaseProgress(req, res) {
    try {
      const { id } = req.params;
      const { progress, progressDescription, itemId } = req.body;
      const currentUser = req.user || {
        id: 1,
        nickname: "系统用户",
        username: "system",
      };

      if (!id) {
        return this.fail(res, "采购订单ID不能为空", 400);
      }

      if (!progress) {
        return this.fail(res, "采购进度不能为空", 400);
      }

      // 验证进度值的有效性
      const validProgress = [
        "待处理",
        "已联系供应商",
        "已下单",
        "生产中",
        "已发货",
        "已收货",
        "已完成",
        "待合同回传",
        "待付款",
        "待下单",
        "无货/停产",
        "待参数确定",
        "货期",
        "待废止",
        "待确认价格",
      ];
      if (!validProgress.includes(progress)) {
        return this.fail(res, "无效的采购进度值", 400);
      }

      // 查询当前订单信息
      const currentOrder = await this.prisma.purchase_order.findUnique({
        where: { id: parseInt(id) },
      });

      if (!currentOrder) {
        return this.fail(res, "采购订单不存在", 404);
      }

      // 如果指定了商品项ID，更新商品项的进度
      if (itemId) {
        // 先验证商品项是否属于该订单
        const itemExists = await this.prisma.purchase_order_item.findFirst({
          where: {
            id: parseInt(itemId),
            purchase_order_id: parseInt(id),
          },
        });

        if (!itemExists) {
          return this.fail(res, "商品项不存在或不属于该订单", 404);
        }

        // 准备更新数据
        const updateData = {
          purchase_progress: progress,
          updated_at: new Date(),
        };

        // 如果进度不是"货期"，清空货期时间
        if (progress !== "货期") {
          updateData.expected_delivery_time = null;
        }

        const updatedItem = await this.prisma.purchase_order_item.update({
          where: {
            id: parseInt(itemId),
          },
          data: updateData,
        });

        // 记录商品项进度更新日志
        await this.prisma.purchase_order_log.create({
          data: {
            purchase_order_id: parseInt(id),
            action_type: "update_item_progress",
            action_description: "更新商品采购进度",
            old_status: null,
            new_status: null,
            status_description: `商品项进度更新为: ${progress}`,
            operator_id: currentUser.id ? BigInt(currentUser.id) : null,
            operator_name: currentUser.nickname || currentUser.username,
            operator_role: currentUser.role || "user",
            details: {
              itemId: itemId,
              progress: progress,
              progressDescription: progressDescription || "",
            },
            remark: progressDescription || `更新商品采购进度为: ${progress}`,
          },
        });

        return this.success(
          res,
          {
            itemId: updatedItem.id,
            progress: updatedItem.purchase_progress,
            updated_at: updatedItem.updated_at,
          },
          "商品采购进度更新成功"
        );
      } else {
        // 更新整个订单的采购状态
        let purchaseStatus = currentOrder.purchase_status;

        // 根据进度自动更新采购状态
        switch (progress) {
          case "待处理":
          case "待合同回传":
          case "待付款":
          case "待下单":
          case "待参数确定":
          case "待确认价格":
            purchaseStatus = 0; // 待处理
            break;
          case "已联系供应商":
          case "已下单":
          case "货期":
            purchaseStatus = 1; // 已分配
            break;
          case "生产中":
          case "已发货":
            purchaseStatus = 2; // 采购中
            break;
          case "已收货":
          case "已完成":
            purchaseStatus = 3; // 已完成
            break;
          case "无货/停产":
          case "待废止":
            purchaseStatus = 4; // 已取消
            break;
        }

        // 准备订单更新数据
        const orderUpdateData = {
          purchase_status: purchaseStatus,
          updater: currentUser.nickname || currentUser.username,
          updated_at: new Date(),
        };

        // 如果进度不是"货期"，清空订单级别的货期时间
        if (progress !== "货期") {
          orderUpdateData.expected_delivery_time = null;
        }

        // 更新主订单状态
        const updatedOrder = await this.prisma.purchase_order.update({
          where: { id: parseInt(id) },
          data: orderUpdateData,
        });

        // 准备商品项更新数据
        const itemUpdateData = {
          purchase_progress: progress,
          item_status: purchaseStatus,
          updated_at: new Date(),
        };

        // 如果进度不是"货期"，清空商品项级别的货期时间
        if (progress !== "货期") {
          itemUpdateData.expected_delivery_time = null;
        }

        // 同时更新所有商品项的采购进度
        await this.prisma.purchase_order_item.updateMany({
          where: {
            purchase_order_id: parseInt(id),
            deleted_at: null,
          },
          data: itemUpdateData,
        });

        // 记录订单进度更新日志
        await this.prisma.purchase_order_log.create({
          data: {
            purchase_order_id: parseInt(id),
            action_type: "update_progress",
            action_description: "更新采购进度",
            old_status: currentOrder.purchase_status,
            new_status: purchaseStatus,
            status_description: `采购进度更新为: ${progress}`,
            operator_id: currentUser.id ? BigInt(currentUser.id) : null,
            operator_name: currentUser.nickname || currentUser.username,
            operator_role: currentUser.role || "user",
            details: {
              progress: progress,
              progressDescription: progressDescription || "",
              oldStatus: currentOrder.purchase_status,
              newStatus: purchaseStatus,
            },
            remark: progressDescription || `更新采购进度为: ${progress}`,
          },
        });

        return this.success(
          res,
          {
            id: updatedOrder.id,
            purchase_status: updatedOrder.purchase_status,
            progress: progress,
            updated_at: updatedOrder.updated_at,
          },
          "采购进度更新成功"
        );
      }
    } catch (error) {
      // 记录错误日志
      console.error("更新采购进度失败", error);

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 设置订单成本总价
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async setCostTotal(req, res) {
    try {
      const { id } = req.params;
      const { costTotal, costType = "manual" } = req.body;
      const currentUser = req.user || {
        id: 1,
        nickname: "系统用户",
        username: "system",
      };

      if (!id) {
        return this.fail(res, "采购订单ID不能为空", 400);
      }

      if (costTotal === undefined || costTotal === null) {
        return this.fail(res, "成本总价不能为空", 400);
      }

      // 验证成本总价格式
      const cost = parseFloat(costTotal);
      if (isNaN(cost) || cost < 0) {
        return this.fail(res, "成本总价必须是有效的非负数", 400);
      }

      // 查询当前订单信息
      const currentOrder = await this.prisma.purchase_order.findUnique({
        where: { id: parseInt(id) },
      });

      if (!currentOrder) {
        return this.fail(res, "采购订单不存在", 404);
      }

      // 计算毛利率
      const totalAmount = parseFloat(currentOrder.total_amount || 0);
      let grossProfitRate = "0.0%";

      if (totalAmount > 0) {
        const profitRate = ((totalAmount - cost) / totalAmount) * 100;
        grossProfitRate = `${profitRate.toFixed(1)}%`;
      }

      // 更新订单成本信息
      const updatedOrder = await this.prisma.purchase_order.update({
        where: { id: parseInt(id) },
        data: {
          purchase_cost: cost,
          updater: currentUser.nickname || currentUser.username,
          updated_at: new Date(),
        },
      });

      // 记录操作日志
      await this.prisma.purchase_order_log.create({
        data: {
          purchase_order_id: parseInt(id),
          action_type: "set_cost_total",
          action_description: "设置订单成本总价",
          old_status: null,
          new_status: null,
          status_description: `设置成本总价为: ¥${cost.toFixed(2)}`,
          operator_id: currentUser.id ? BigInt(currentUser.id) : null,
          operator_name: currentUser.nickname || currentUser.username,
          operator_role: currentUser.role || "user",
          details: {
            oldCost: parseFloat(currentOrder.purchase_cost || 0),
            newCost: cost,
            costType: costType,
            totalAmount: totalAmount,
            grossProfitRate: grossProfitRate,
          },
          remark: `设置成本总价为: ¥${cost.toFixed(
            2
          )}, 毛利率: ${grossProfitRate}`,
        },
      });

      // 返回成功响应
      this.success(
        res,
        {
          id: updatedOrder.id,
          purchase_cost: updatedOrder.purchase_cost,
          total_amount: updatedOrder.total_amount,
          gross_profit_rate: grossProfitRate,
          updated_at: updatedOrder.updated_at,
        },
        "订单成本总价设置成功"
      );
    } catch (error) {
      // 记录错误日志
      console.error("设置订单成本总价失败", error);

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 设置商品项成本总价
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async setProductCostTotal(req, res) {
    try {
      const { productId } = req.params;
      const { costTotal } = req.body;
      const currentUser = req.user || {
        id: 1,
        nickname: "系统用户",
        username: "system",
      };

      if (!productId) {
        return this.fail(res, "商品项ID不能为空", 400);
      }

      if (costTotal === undefined || costTotal === null) {
        return this.fail(res, "成本总价不能为空", 400);
      }

      // 验证成本总价格式
      const cost = parseFloat(costTotal);
      if (isNaN(cost) || cost < 0) {
        return this.fail(res, "成本总价必须是有效的非负数", 400);
      }

      // 查询当前商品项信息
      const currentProduct = await this.prisma.purchase_order_item.findUnique({
        where: { id: parseInt(productId) },
        include: {
          purchase_order: true,
        },
      });

      if (!currentProduct) {
        return this.fail(res, "商品项不存在", 404);
      }

      // 更新商品项成本信息
      const updatedProduct = await this.prisma.purchase_order_item.update({
        where: { id: parseInt(productId) },
        data: {
          cost_total: cost,
          updated_at: new Date(),
        },
      });

      // 记录操作日志
      await this.prisma.purchase_order_log.create({
        data: {
          purchase_order_id: BigInt(currentProduct.purchase_order_id),
          action_type: "set_product_cost_total",
          action_description: `设置商品项成本总价: ${currentProduct.product_name}`,
          old_status: null,
          new_status: null,
          operator_name: currentUser.nickname || currentUser.username,
          operator_id: BigInt(currentUser.id),
          created_at: new Date(),
        },
      });

      return this.success(res, "设置商品项成本总价成功", {
        id: updatedProduct.id,
        costTotal: cost,
      });
    } catch (error) {
      console.error("设置商品项成本总价失败:", error);
      return this.fail(res, "设置商品项成本总价失败", 500);
    }
  }

  /**
   * 获取亏损原因文本描述
   * @param {string} lossReason - 亏损原因代码
   * @returns {string} - 亏损原因文本
   */
  getLossReasonText(lossReason) {
    const lossReasonMap = {
      freight_loss: "运费亏损",
      packaging_loss: "特殊包装亏损",
      installation_loss: "安装费亏损",
      tax_loss: "税金亏损",
      no_quote: "未参与报价",
      low_quantity: "采购数量少",
      quote_error: "开发报价错误",
      quick_removal: "快上快下未及时下架",
      discontinued: "产品型号停产",
      relationship_order: "客情自然单",
      other: "其他",
    };
    return lossReasonMap[lossReason] || lossReason;
  }

  /**
   * 设置商品项亏损原因
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async setProductLossReason(req, res) {
    try {
      const { productId } = req.params;
      const { lossReason } = req.body;
      const currentUser = req.user || {
        id: 1,
        nickname: "系统用户",
        username: "system",
      };

      if (!productId) {
        return this.fail(res, "商品项ID不能为空", 400);
      }

      if (!lossReason) {
        return this.fail(res, "亏损原因不能为空", 400);
      }

      // 查询当前商品项信息
      const currentProduct = await this.prisma.purchase_order_item.findUnique({
        where: { id: parseInt(productId) },
        include: {
          purchase_order: true,
        },
      });

      if (!currentProduct) {
        return this.fail(res, "商品项不存在", 404);
      }

      // 更新商品项亏损原因
      const updatedProduct = await this.prisma.purchase_order_item.update({
        where: { id: parseInt(productId) },
        data: {
          loss_reason: lossReason,
          updated_at: new Date(),
        },
      });

      // 记录操作日志
      await this.prisma.purchase_order_log.create({
        data: {
          purchase_order_id: BigInt(currentProduct.purchase_order_id),
          action_type: "set_product_loss_reason",
          action_description: `设置商品项亏损原因: ${currentProduct.product_name} - ${lossReason}`,
          old_status: null,
          new_status: null,
          operator_name: currentUser.nickname || currentUser.username,
          operator_id: BigInt(currentUser.id),
          created_at: new Date(),
        },
      });

      return this.success(res, "设置商品项亏损原因成功", {
        id: updatedProduct.id,
        lossReason: lossReason,
      });
    } catch (error) {
      console.error("设置商品项亏损原因失败:", error);
      return this.fail(res, "设置商品项亏损原因失败", 500);
    }
  }

  /**
   * 设置实际供应商
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async setActualSupplier(req, res) {
    try {
      const { id } = req.params;
      const { supplierId, supplierName, supplierCode, itemId, quotationPrice } = req.body;
      const currentUser = req.user || {
        id: 1,
        nickname: "系统用户",
        username: "system",
      };

      if (!id) {
        return this.fail(res, "采购订单ID不能为空", 400);
      }

      if (!supplierName) {
        return this.fail(res, "供应商名称不能为空", 400);
      }

      // 查询当前订单信息
      const currentOrder = await this.prisma.purchase_order.findUnique({
        where: { id: parseInt(id) },
      });

      if (!currentOrder) {
        return this.fail(res, "采购订单不存在", 404);
      }

      // 如果指定了商品项ID，更新商品项的实际供应商
      if (itemId) {
        // 先验证商品项是否属于该订单
        const itemExists = await this.prisma.purchase_order_item.findFirst({
          where: {
            id: parseInt(itemId),
            purchase_order_id: parseInt(id),
          },
        });

        if (!itemExists) {
          return this.fail(res, "商品项不存在或不属于该订单", 404);
        }

        // 构建更新数据
        const updateData = {
          actual_supplier_id: supplierId ? parseInt(supplierId) : null,
          actual_supplier_name: supplierName,
          updated_at: new Date(),
        };

        // 如果提供了报价金额，更新实际成本
        if (quotationPrice && quotationPrice > 0) {
          updateData.actual_cost = parseFloat(quotationPrice);
          // 计算成本总价 = 实际成本 * 数量
          // updateData.cost_total = parseFloat(quotationPrice) * (itemExists.quantity || 1);
        }

        const updatedItem = await this.prisma.purchase_order_item.update({
          where: {
            id: parseInt(itemId),
          },
          data: updateData,
        });

        // 记录商品项供应商更新日志
        const logDescription = quotationPrice
          ? `设置商品实际供应商为: ${supplierName}，报价金额: ${quotationPrice}元`
          : `设置商品实际供应商为: ${supplierName}`;

        await this.prisma.purchase_order_log.create({
          data: {
            purchase_order_id: parseInt(id),
            action_type: "set_item_supplier",
            action_description: "设置商品实际供应商",
            old_status: null,
            new_status: null,
            status_description: logDescription,
            operator_id: currentUser.id ? BigInt(currentUser.id) : null,
            operator_name: currentUser.nickname || currentUser.username,
            operator_role: currentUser.role || "user",
            details: {
              itemId: itemId,
              supplierId: supplierId,
              supplierName: supplierName,
              supplierCode: supplierCode || "",
              quotationPrice: quotationPrice || null,
              actualCost: quotationPrice || null,
              costTotal: quotationPrice ? parseFloat(quotationPrice) * (itemExists.quantity || 1) : null,
            },
            remark: logDescription,
          },
        });

        return this.success(
          res,
          {
            itemId: updatedItem.id,
            actual_supplier_id: updatedItem.actual_supplier_id,
            actual_supplier_name: updatedItem.actual_supplier_name,
            actual_cost: updatedItem.actual_cost,
            cost_total: updatedItem.cost_total,
            quotation_price: quotationPrice,
            updated_at: updatedItem.updated_at,
          },
          quotationPrice
            ? `商品实际供应商设置成功，实际成本已更新为 ${quotationPrice} 元`
            : "商品实际供应商设置成功"
        );
      } else {
        // 由于供应商是商品维度的，这里应该返回错误
        return this.fail(
          res,
          "请指定商品项ID，供应商设置是商品级别的操作",
          400
        );
      }
    } catch (error) {
      // 记录错误日志
      console.error("设置实际供应商失败", error);

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 更新税收分类
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async updateTaxClassification(req, res) {
    try {
      const { id } = req.params;
      const {
        itemId,
        taxCategory,
        taxClassificationCode,
        taxClassificationName,
      } = req.body;
      const currentUser = req.user || {
        id: 1,
        nickname: "系统用户",
        username: "system",
      };

      if (!id) {
        return this.fail(res, "采购订单ID不能为空", 400);
      }

      if (!itemId) {
        return this.fail(res, "商品项ID不能为空", 400);
      }

      if (!taxCategory) {
        return this.fail(res, "税收分类不能为空", 400);
      }

      // 查询当前订单信息
      const currentOrder = await this.prisma.purchase_order.findUnique({
        where: { id: parseInt(id) },
      });

      if (!currentOrder) {
        return this.fail(res, "采购订单不存在", 404);
      }

      // 查询当前商品项信息
      const currentItem = await this.prisma.purchase_order_item.findFirst({
        where: {
          id: parseInt(itemId),
          purchase_order_id: parseInt(id),
        },
      });

      if (!currentItem) {
        return this.fail(res, "商品项不存在", 404);
      }

      // 更新商品项的税收分类
      const updatedItem = await this.prisma.purchase_order_item.update({
        where: {
          id: parseInt(itemId),
        },
        data: {
          tax_category: taxCategory,
          updated_at: new Date(),
        },
      });

      // 记录税收分类更新日志
      await this.prisma.purchase_order_log.create({
        data: {
          purchase_order_id: parseInt(id),
          action_type: "update_tax_classification",
          action_description: "更新商品税收分类",
          old_status: null,
          new_status: null,
          status_description: `商品税收分类更新为: ${taxCategory}`,
          operator_id: currentUser.id ? BigInt(currentUser.id) : null,
          operator_name: currentUser.nickname || currentUser.username,
          operator_role: currentUser.role || "user",
          details: {
            itemId: itemId,
            oldTaxCategory: currentItem.tax_category,
            newTaxCategory: taxCategory,
            taxClassificationCode: taxClassificationCode || "",
            taxClassificationName: taxClassificationName || "",
          },
          remark: `更新商品税收分类为: ${taxCategory}`,
        },
      });

      // 检查订单中所有商品是否都已设置税收分类
      const allItems = await this.prisma.purchase_order_item.findMany({
        where: {
          purchase_order_id: parseInt(id),
          deleted_at: null,
        },
      });

      const allHaveTaxCategory = allItems.every(
        (item) => item.tax_category && item.tax_category.trim() !== ""
      );

      // 如果所有商品都已设置税收分类，可以考虑更新订单的链接状态
      if (allHaveTaxCategory) {
        await this.prisma.purchase_order.update({
          where: { id: parseInt(id) },
          data: {
            link_status: 1, // 可以生成链接
            updater: currentUser.nickname || currentUser.username,
            updated_at: new Date(),
          },
        });
      }

      // 返回成功响应
      this.success(
        res,
        {
          itemId: updatedItem.id,
          tax_category: updatedItem.tax_category,
          all_items_have_tax: allHaveTaxCategory,
          can_generate_link: allHaveTaxCategory,
          updated_at: updatedItem.updated_at,
        },
        "商品税收分类更新成功"
      );
    } catch (error) {
      // 记录错误日志
      console.error("更新税收分类失败", error);

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 保存亏损原因
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async saveLossReason(req, res) {
    try {
      const { id } = req.params;
      const { lossReason, lossType = "other" } = req.body;
      const currentUser = req.user || {
        id: 1,
        nickname: "系统用户",
        username: "system",
      };

      if (!id) {
        return this.fail(res, "采购订单ID不能为空", 400);
      }

      if (!lossReason || lossReason.trim() === "") {
        return this.fail(res, "亏损原因不能为空", 400);
      }

      // 查询当前订单信息
      const currentOrder = await this.prisma.purchase_order.findUnique({
        where: { id: parseInt(id) },
      });

      if (!currentOrder) {
        return this.fail(res, "采购订单不存在", 404);
      }

      // 更新亏损原因和亏损状态
      const updatedOrder = await this.prisma.purchase_order.update({
        where: { id: parseInt(id) },
        data: {
          loss_reason: lossReason.trim(),
          is_loss: 1, // 设置为亏损状态
          updater: currentUser.nickname || currentUser.username,
          updated_at: new Date(),
        },
      });

      // 记录操作日志
      await this.prisma.purchase_order_log.create({
        data: {
          purchase_order_id: parseInt(id),
          action_type: "save_loss_reason",
          action_description: "保存亏损原因",
          old_status: null,
          new_status: null,
          status_description: `设置亏损原因: ${lossReason.trim()}`,
          operator_id: currentUser.id ? BigInt(currentUser.id) : null,
          operator_name: currentUser.nickname || currentUser.username,
          operator_role: currentUser.role || "user",
          details: {
            oldLossReason: currentOrder.loss_reason,
            newLossReason: lossReason.trim(),
            lossType: lossType,
            oldIsLoss: currentOrder.is_loss,
            newIsLoss: 1,
          },
          remark: `保存亏损原因: ${lossReason.trim()}`,
        },
      });

      // 返回成功响应
      this.success(
        res,
        {
          id: updatedOrder.id,
          loss_reason: updatedOrder.loss_reason,
          is_loss: updatedOrder.is_loss,
          updated_at: updatedOrder.updated_at,
        },
        "亏损原因保存成功"
      );
    } catch (error) {
      // 记录错误日志
      console.error("保存亏损原因失败", error);

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 设置货期时间
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async setExpectedDeliveryTime(req, res) {
    try {
      const { id } = req.params;
      const { expected_delivery_time, itemId } = req.body;
      const currentUser = req.user || {
        id: 1,
        nickname: "系统用户",
        username: "system",
      };

      if (!id) {
        return this.fail(res, "采购订单ID不能为空", 400);
      }

      if (!expected_delivery_time) {
        return this.fail(res, "预期交货时间不能为空", 400);
      }

      // 验证时间戳格式（13位）
      if (!/^\d{13}$/.test(expected_delivery_time.toString())) {
        return this.fail(res, "预期交货时间格式错误，需要13位时间戳", 400);
      }

      // 将13位时间戳转换为Date对象
      const deliveryDate = new Date(parseInt(expected_delivery_time));

      // 验证日期是否有效
      if (isNaN(deliveryDate.getTime())) {
        return this.fail(res, "无效的时间戳", 400);
      }

      // 查询当前订单信息
      const currentOrder = await this.prisma.purchase_order.findUnique({
        where: { id: parseInt(id) },
      });

      if (!currentOrder) {
        return this.fail(res, "采购订单不存在", 404);
      }

      if (itemId) {
        // 更新单个商品项的预期交货时间
        const itemExists = await this.prisma.purchase_order_item.findFirst({
          where: {
            id: parseInt(itemId),
            purchase_order_id: parseInt(id),
          },
        });

        if (!itemExists) {
          return this.fail(res, "商品项不存在或不属于该订单", 404);
        }

        const updatedItem = await this.prisma.purchase_order_item.update({
          where: {
            id: parseInt(itemId),
          },
          data: {
            expected_delivery_time: deliveryDate,
            updated_at: new Date(),
          },
        });

        // 记录商品项货期时间更新日志
        await this.prisma.purchase_order_log.create({
          data: {
            purchase_order_id: parseInt(id),
            action_type: "update_item_delivery_time",
            action_description: "更新商品预期交货时间",
            old_status: null,
            new_status: null,
            status_description: `商品项预期交货时间更新为: ${deliveryDate.toISOString()}`,
            operator_id: currentUser.id ? BigInt(currentUser.id) : null,
            operator_name: currentUser.nickname || currentUser.username,
            operator_role: currentUser.role || "user",
            details: {
              itemId: itemId,
              expectedDeliveryTime: expected_delivery_time,
              deliveryDate: deliveryDate.toISOString(),
            },
            remark: `更新商品预期交货时间为: ${deliveryDate.toLocaleDateString(
              "zh-CN"
            )}`,
          },
        });

        return this.success(
          res,
          {
            itemId: updatedItem.id,
            expected_delivery_time: updatedItem.expected_delivery_time,
            updated_at: updatedItem.updated_at,
          },
          "商品预期交货时间更新成功"
        );
      } else {
        // 更新整个订单的预期交货时间
        const updatedOrder = await this.prisma.purchase_order.update({
          where: { id: parseInt(id) },
          data: {
            expected_delivery_time: deliveryDate,
            updater: currentUser.nickname || currentUser.username,
            updated_at: new Date(),
          },
        });

        // 同时更新所有商品项的预期交货时间
        await this.prisma.purchase_order_item.updateMany({
          where: {
            purchase_order_id: parseInt(id),
            deleted_at: null,
          },
          data: {
            expected_delivery_time: deliveryDate,
            updated_at: new Date(),
          },
        });

        // 记录订单货期时间更新日志
        await this.prisma.purchase_order_log.create({
          data: {
            purchase_order_id: parseInt(id),
            action_type: "update_delivery_time",
            action_description: "更新订单预期交货时间",
            old_status: null, // 货期时间更新不涉及状态变更
            new_status: null, // 货期时间更新不涉及状态变更
            status_description: `订单预期交货时间更新为: ${deliveryDate.toISOString()}`,
            operator_id: currentUser.id ? BigInt(currentUser.id) : null,
            operator_name: currentUser.nickname || currentUser.username,
            operator_role: currentUser.role || "user",
            details: {
              expectedDeliveryTime: expected_delivery_time,
              deliveryDate: deliveryDate.toISOString(),
              oldDeliveryTime:
                currentOrder.expected_delivery_time?.toISOString() || null,
            },
            remark: `更新订单预期交货时间为: ${deliveryDate.toLocaleDateString(
              "zh-CN"
            )}`,
          },
        });

        return this.success(
          res,
          {
            id: updatedOrder.id,
            expected_delivery_time: updatedOrder.expected_delivery_time,
            updated_at: updatedOrder.updated_at,
          },
          "订单预期交货时间更新成功"
        );
      }
    } catch (error) {
      // 记录错误日志
      console.error("设置货期时间失败", error);

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 生成发货链接
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async generateShippingLink(req, res) {
    try {
      const { id } = req.params;
      const {
        recipientName,
        recipientPhone,
        recipientAddress,
        shippingType = 1
      } = req.body;

      const currentUser = req.user || {
        id: 1,
        nickname: "系统用户",
        username: "system",
      };

      // 参数验证
      if (!id) {
        return this.fail(res, "采购订单ID不能为空", 400);
      }

      if (!recipientName || !recipientName.trim()) {
        return this.fail(res, "收货人姓名不能为空", 400);
      }

      if (!recipientPhone || !recipientPhone.trim()) {
        return this.fail(res, "收货人电话不能为空", 400);
      }

      if (!recipientAddress || !recipientAddress.trim()) {
        return this.fail(res, "收货地址不能为空", 400);
      }

      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(recipientPhone)) {
        return this.fail(res, "请输入正确的手机号码", 400);
      }

      // 查询采购订单信息
      const purchaseOrder = await this.prisma.purchase_order.findUnique({
        where: { id: parseInt(id) },
        include: {
          purchase_order_shipping_link: true, // 检查是否已存在发货链接
        },
      });

      if (!purchaseOrder) {
        return this.fail(res, "采购订单不存在", 404);
      }

      // 检查是否已经生成过发货链接
      if (purchaseOrder.purchase_order_shipping_link) {
        return this.fail(res, "该采购订单已生成发货链接，不能重复生成", 409);
      }

      // 检查订单状态是否允许生成链接
      if (purchaseOrder.link_status !== 1) {
        return this.fail(res, "订单状态不允许生成发货链接，请先完成税收分类设置", 400);
      }

      // 获取前端域名（从请求头获取）
      const frontendOrigin = req.get('origin') || req.get('referer');
      let frontendDomain = 'https://localhost:3000'; // 默认值

      if (frontendOrigin) {
        try {
          const url = new URL(frontendOrigin);
          frontendDomain = `${url.protocol}//${url.host}`;
        } catch (error) {
          console.warn('解析前端域名失败，使用默认值:', error.message);
        }
      }

      // 先创建发货链接记录以获取ID
      const shippingLinkRecord = await this.prisma.purchase_order_shipping_link.create({
        data: {
          shipping_link: '', // 临时为空，稍后更新
          qr_code_url: null, // 临时为空，稍后更新
          original_order_number: purchaseOrder.original_order_number,
          purchase_order_id: parseInt(id),
          purchase_order_number: purchaseOrder.purchase_order_number,
          split_order_id: null, // 采购订单级别的链接，拆分订单ID为空
          split_order_number: null,
          recipient_name: recipientName.trim(),
          recipient_phone: recipientPhone.trim(),
          recipient_address: recipientAddress.trim(),
          shipping_type: parseInt(shippingType),
          operator_id: BigInt(currentUser.id),
          operator_name: currentUser.nickname || currentUser.username,
          generated_at: new Date(),
          created_at: new Date(),
          updated_at: new Date(),
        },
      });

      // 使用链接记录ID生成发货链接
      const shippingLink = `${frontendDomain}/h5/SupplyChain/HavedShipped/${shippingLinkRecord.id}`;

      // 生成二维码
      let qrCodeBuffer;
      try {
        qrCodeBuffer = await QRCode.toBuffer(shippingLink, {
          type: 'png',
          width: 300,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        });
      } catch (qrError) {
        console.error('生成二维码失败:', qrError);
        return this.fail(res, "生成二维码失败", 500);
      }

      // 上传二维码图片
      let qrCodeUrl = null;
      try {
        // 获取上传服务
        const uploadService = await this.integrationFactory.getDefaultService('Upload');

        // 生成文件名
        const fileName = `qrcode_${purchaseOrder.purchase_order_number}_${shippingLinkRecord.id}_${Date.now()}.png`;

        // 上传二维码
        const uploadResult = await uploadService.uploadBuffer(
          qrCodeBuffer,
          fileName,
          'qrcodes/shipping',
          { contentType: 'image/png' }
        );

        qrCodeUrl = uploadResult.fileUrl;
      } catch (uploadError) {
        console.error('上传二维码失败:', uploadError);
        return this.fail(res, "上传二维码失败", 500);
      }

      // 更新发货链接记录，填入真实的链接和二维码URL
      const updatedShippingLinkRecord = await this.prisma.purchase_order_shipping_link.update({
        where: { id: shippingLinkRecord.id },
        data: {
          shipping_link: shippingLink,
          qr_code_url: qrCodeUrl,
          updated_at: new Date(),
        },
      });

      // 更新采购订单的链接生成状态
      await this.prisma.purchase_order.update({
        where: { id: parseInt(id) },
        data: {
          link_generated: true,
          updater: currentUser.nickname || currentUser.username,
          updated_at: new Date(),
        },
      });

      // 记录操作日志
      await this.prisma.purchase_order_log.create({
        data: {
          purchase_order_id: parseInt(id),
          action_type: "generate_shipping_link",
          action_description: "生成发货链接",
          old_status: null,
          new_status: null,
          status_description: `生成发货链接：${shippingLink}，链接ID：${shippingLinkRecord.id}`,
          operator_id: BigInt(currentUser.id),
          operator_name: currentUser.nickname || currentUser.username,
          operator_role: currentUser.role || "user",
          details: {
            linkId: shippingLinkRecord.id,
            shippingLink: shippingLink,
            qrCodeUrl: qrCodeUrl,
            recipientName: recipientName,
            recipientPhone: recipientPhone,
            recipientAddress: recipientAddress,
            shippingType: shippingType,
          },
          remark: `生成发货链接，链接ID：${shippingLinkRecord.id}，收货人：${recipientName}，电话：${recipientPhone}`,
        },
      });

      // 返回成功响应
      return this.success(
        res,
        {
          id: updatedShippingLinkRecord.id,
          shippingLink: updatedShippingLinkRecord.shipping_link,
          qrCodeUrl: updatedShippingLinkRecord.qr_code_url,
          recipientName: updatedShippingLinkRecord.recipient_name,
          recipientPhone: updatedShippingLinkRecord.recipient_phone,
          recipientAddress: updatedShippingLinkRecord.recipient_address,
          shippingType: updatedShippingLinkRecord.shipping_type,
          generatedAt: updatedShippingLinkRecord.generated_at,
        },
        "发货链接生成成功"
      );
    } catch (error) {
      // 记录错误日志
      console.error("生成发货链接失败:", error);

      // 根据错误类型返回不同的响应
      if (error.code === 'P2002') {
        return this.fail(res, "该采购订单已生成发货链接，不能重复生成", 409);
      }

      // 返回通用错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取发货链接信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async getShippingLink(req, res) {
    try {
      const { id } = req.params;

      // 参数验证
      if (!id) {
        return this.fail(res, "采购订单ID不能为空", 400);
      }

      // 查询采购订单及其发货链接信息
      const purchaseOrder = await this.prisma.purchase_order.findUnique({
        where: { id: parseInt(id) },
        include: {
          purchase_order_shipping_link: true, // 包含发货链接信息
        },
      });

      if (!purchaseOrder) {
        return this.fail(res, "采购订单不存在", 404);
      }

      // 检查是否存在发货链接
      if (!purchaseOrder.purchase_order_shipping_link) {
        return this.fail(res, "该采购订单尚未生成发货链接", 404);
      }

      const shippingLink = purchaseOrder.purchase_order_shipping_link;

      // 返回发货链接信息
      return this.success(
        res,
        {
          id: shippingLink.id,
          shippingLink: shippingLink.shipping_link,
          qrCodeUrl: shippingLink.qr_code_url,
          recipientName: shippingLink.recipient_name,
          recipientPhone: shippingLink.recipient_phone,
          recipientAddress: shippingLink.recipient_address,
          shippingType: shippingLink.shipping_type,
          generatedAt: shippingLink.generated_at,
          orderInfo: {
            id: purchaseOrder.id,
            purchaseOrderNumber: purchaseOrder.purchase_order_number,
            originalOrderNumber: purchaseOrder.original_order_number,
            supplierName: purchaseOrder.supplier_name,
            totalAmount: purchaseOrder.total_amount,
          },
        },
        "获取发货链接信息成功"
      );
    } catch (error) {
      // 记录错误日志
      console.error("获取发货链接信息失败:", error);

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }
}

module.exports = PurchaseOrderController;
