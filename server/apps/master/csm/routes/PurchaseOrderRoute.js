/**
 * 采购订单路由
 * 处理采购订单相关的路由配置
 */
const express = require("express");
const router = express.Router();
const { prisma } = require("../../../../core/database/prisma");
const PurchaseOrderController = require("../controllers/PurchaseOrderController");
const RouterConfig = require("../../../../core/routes/RouterConfig");

// 创建控制器实例
const controller = new PurchaseOrderController(prisma);

// 创建受JWT保护的路由
const protectedRouter = RouterConfig.authRoute(router);

/**
 * @swagger
 * components:
 *   schemas:
 *     PurchaseOrderApplication:
 *       type: object
 *       required:
 *         - orderId
 *         - sourceSupplier
 *         - actualReceiver
 *         - contactPhone
 *         - actualAddress
 *       properties:
 *         orderId:
 *           type: integer
 *           description: 原始订单ID
 *         sourceSupplier:
 *           type: string
 *           description: 采购员
 *         actualReceiver:
 *           type: string
 *           description: 实际收货人
 *         contactPhone:
 *           type: string
 *           description: 联系电话
 *         actualAddress:
 *           type: string
 *           description: 实际收货地址
 *         businessManager:
 *           type: string
 *           description: 业务员备注
 *         orderRemark:
 *           type: string
 *           description: 订单备注
 *         customerRemark:
 *           type: string
 *           description: 客户备注
 *         attachments:
 *           type: array
 *           description: 附件列表
 *           items:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: 文件名
 *               url:
 *                 type: string
 *                 description: 文件URL
 *               size:
 *                 type: integer
 *                 description: 文件大小
 *               type:
 *                 type: string
 *                 description: 文件类型
 */

/**
 * @swagger
 * /api/master/csm/purchase-orders/apply:
 *   post:
 *     summary: 申请采购订单
 *     description: 根据原始订单信息创建采购订单申请
 *     tags: [采购订单管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PurchaseOrderApplication'
 *     responses:
 *       201:
 *         description: 采购订单申请成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 采购订单申请成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       description: 采购订单ID
 *                     purchaseOrderNumber:
 *                       type: string
 *                       description: 采购订单编号
 *                     originalOrderId:
 *                       type: integer
 *                       description: 原始订单ID
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 原始订单不存在
 *       409:
 *         description: 该订单已经申请过采购订单
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.post("/apply", controller.applyPurchaseOrder.bind(controller));

/**
 * @swagger
 * /api/master/csm/purchase-orders/purchaser/{orderNumber}:
 *   get:
 *     summary: 根据原始订单号获取采购员信息和商品信息
 *     description: 根据原始订单号查找对应的采购订单，返回采购员信息和商品信息。如果存在拆分商品则返回拆分订单信息，否则返回原订单商品信息
 *     tags: [采购订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: orderNumber
 *         required: true
 *         schema:
 *           type: string
 *         description: 原始订单号
 *     responses:
 *       200:
 *         description: 获取订单信息成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "获取订单信息成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     purchaser:
 *                       type: string
 *                       description: 采购员姓名
 *                     follower:
 *                       type: string
 *                       description: 跟单员姓名
 *                     buyer:
 *                       type: string
 *                       description: 买手姓名
 *                     splitStatus:
 *                       type: integer
 *                       description: 拆分状态：0-未拆分 1-部分拆分 2-全部拆分 3-拆分中
 *                     splitCount:
 *                       type: integer
 *                       description: 拆分次数
 *                     products:
 *                       type: array
 *                       description: 商品信息列表
 *                       items:
 *                         type: object
 *                         properties:
 *                           splitNumber:
 *                             type: string
 *                             description: 拆分单号（如果是拆分商品）
 *                           splitType:
 *                             type: string
 *                             description: 类型：original-原订单 split-拆分订单
 *                           items:
 *                             type: array
 *                             description: 商品项列表
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       description: 创建时间
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                       description: 更新时间
 *       404:
 *         description: 未找到该订单的采购员信息
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.get(
  "/purchaser/:orderNumber",
  controller.getPurchaserByOrderNumber.bind(controller)
);

/**
 * @swagger
 * /api/master/csm/purchase-orders/purchaser-by-purchase-order/{purchaseOrderNumber}:
 *   get:
 *     summary: 根据采购订单号获取采购员信息
 *     description: 根据采购订单号查找对应的采购订单，返回采购员相关信息
 *     tags: [采购订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: purchaseOrderNumber
 *         required: true
 *         schema:
 *           type: string
 *         description: 采购订单号
 *     responses:
 *       200:
 *         description: 获取采购员信息成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取采购员信息成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       description: 采购订单ID
 *                     purchaseOrderNumber:
 *                       type: string
 *                       description: 采购订单号
 *                     originalOrderNumber:
 *                       type: string
 *                       description: 原始订单号
 *                     purchaserId:
 *                       type: integer
 *                       description: 采购员ID
 *                     purchaserName:
 *                       type: string
 *                       description: 采购员姓名
 *                     follower:
 *                       type: string
 *                       description: 跟单员
 *                     buyerAccount:
 *                       type: string
 *                       description: 买家账号
 *                     supplierId:
 *                       type: integer
 *                       description: 供应商ID
 *                     supplierName:
 *                       type: string
 *                       description: 供应商名称
 *                     orderSource:
 *                       type: string
 *                       description: 订单来源
 *                     orderType:
 *                       type: string
 *                       description: 订单类型
 *                     purchaseStatus:
 *                       type: integer
 *                       description: 采购状态
 *                     orderStatus:
 *                       type: integer
 *                       description: 订单状态
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       description: 创建时间
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                       description: 更新时间
 *       400:
 *         description: 采购订单号不能为空
 *       404:
 *         description: 未找到该采购订单的采购员信息
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.get(
  "/purchaser-by-purchase-order/:purchaseOrderNumber",
  controller.getPurchaserByPurchaseOrderNumber.bind(controller)
);

/**
 * @swagger
 * /api/master/csm/purchase-orders:
 *   get:
 *     summary: 获取采购订单列表
 *     description: 获取采购订单列表，支持分页和筛选
 *     tags: [采购订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: purchaseOrderNumber
 *         schema:
 *           type: string
 *         description: 采购订单编号
 *       - in: query
 *         name: originalOrderId
 *         schema:
 *           type: integer
 *         description: 原始订单ID
 *       - in: query
 *         name: purchaseStatus
 *         schema:
 *           type: integer
 *         description: 采购状态
 *       - in: query
 *         name: purchaserName
 *         schema:
 *           type: string
 *         description: 采购员姓名
 *       - in: query
 *         name: supplierName
 *         schema:
 *           type: string
 *         description: 供应商名称
 *       - in: query
 *         name: startTime
 *         schema:
 *           type: integer
 *         description: 开始时间戳
 *       - in: query
 *         name: endTime
 *         schema:
 *           type: integer
 *         description: 结束时间戳
 *     responses:
 *       200:
 *         description: 获取采购订单列表成功
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.get("/", controller.getPurchaseOrders.bind(controller));

/**
 * @swagger
 * /api/master/csm/purchase-orders/{id}:
 *   get:
 *     summary: 获取采购订单详情
 *     description: 根据采购订单ID获取详细信息
 *     tags: [采购订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 采购订单ID
 *     responses:
 *       200:
 *         description: 获取采购订单详情成功
 *       404:
 *         description: 采购订单不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.get("/:id", controller.getPurchaseOrderDetail.bind(controller));

/**
 * @swagger
 * /api/master/csm/purchase-orders/{id}/v2:
 *   get:
 *     summary: 获取采购订单详情（新版本）
 *     description: 根据采购订单ID获取分离格式的详细信息，返回采购订单信息、原订单信息和关联数据
 *     tags: [采购订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 采购订单ID
 *     responses:
 *       200:
 *         description: 获取采购订单详情成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取采购订单详情成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     purchaseOrderInfo:
 *                       type: object
 *                       description: 采购订单信息（排除原订单相关字段）
 *                     originalOrderInfo:
 *                       type: object
 *                       description: 原订单信息
 *                     relatedData:
 *                       type: object
 *                       description: 关联数据（商品项、附件、日志、费用单等）
 *       404:
 *         description: 采购订单不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.get(
  "/:id/v2",
  controller.getPurchaseOrderDetailV2.bind(controller)
);

/**
 * @swagger
 * /api/master/csm/purchase-orders/{id}/detail:
 *   get:
 *     summary: 获取采购订单完整详情
 *     description: 根据采购订单ID获取包含所有关联数据的完整详细信息
 *     tags: [采购订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 采购订单ID
 *     responses:
 *       200:
 *         description: 获取采购订单完整详情成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取采购订单详情成功
 *                 data:
 *                   type: object
 *                   description: 完整的采购订单详情数据
 *       404:
 *         description: 采购订单不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.get(
  "/:id/detail",
  controller.getPurchaseOrderDetail.bind(controller)
);

/**
 * @swagger
 * /api/master/csm/purchase-orders/{id}/status:
 *   put:
 *     summary: 更新采购订单状态
 *     description: 更新采购订单的状态信息
 *     tags: [采购订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 采购订单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - purchaseStatus
 *             properties:
 *               purchaseStatus:
 *                 type: integer
 *                 description: 采购状态
 *               purchaserName:
 *                 type: string
 *                 description: 采购员姓名
 *               supplierName:
 *                 type: string
 *                 description: 供应商名称
 *               remark:
 *                 type: string
 *                 description: 备注
 *     responses:
 *       200:
 *         description: 更新采购订单状态成功
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 采购订单不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.put(
  "/:id/status",
  controller.updatePurchaseOrderStatus.bind(controller)
);

/**
 * @swagger
 * /api/master/csm/purchase-orders/{id}/change-purchaser:
 *   put:
 *     summary: 更换采购员
 *     description: 更换指定采购订单的采购员
 *     tags: [采购订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 采购订单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - newPurchaser
 *               - changeReason
 *             properties:
 *               newPurchaser:
 *                 type: string
 *                 description: 新采购员姓名
 *               changeReason:
 *                 type: string
 *                 description: 更换原因
 *     responses:
 *       200:
 *         description: 采购员更换成功
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 采购订单不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.put(
  "/:id/change-purchaser",
  controller.changePurchaser.bind(controller)
);

/**
 * @swagger
 * /api/master/csm/purchase-orders/{id}/update-remark:
 *   put:
 *     summary: 更新采购备注
 *     description: 更新指定采购订单的备注信息
 *     tags: [采购订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 采购订单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - newRemark
 *             properties:
 *               newRemark:
 *                 type: string
 *                 description: 新增备注内容
 *               remarkType:
 *                 type: string
 *                 enum: [normal, urgent, warning, quality, logistics]
 *                 default: normal
 *                 description: 备注类型
 *     responses:
 *       200:
 *         description: 采购备注更新成功
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 采购订单不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.put(
  "/:id/update-remark",
  controller.updatePurchaseRemark.bind(controller)
);

/**
 * @swagger
 * /api/master/csm/purchase-orders/{id}/update-progress:
 *   put:
 *     summary: 更新采购进度
 *     description: 更新指定采购订单或商品项的采购进度
 *     tags: [采购订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 采购订单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - progress
 *             properties:
 *               progress:
 *                 type: string
 *                 enum: [待处理, 已联系供应商, 已下单, 生产中, 已发货, 已收货, 已完成, 待合同回传, 待付款, 待下单, 无货/停产, 待参数确定, 货期, 待废止, 待确认价格]
 *                 description: 采购进度
 *               progressDescription:
 *                 type: string
 *                 description: 进度描述
 *               itemId:
 *                 type: integer
 *                 description: 商品项ID（可选，如果指定则更新商品项进度）
 *     responses:
 *       200:
 *         description: 采购进度更新成功
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 采购订单不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.put(
  "/:id/update-progress",
  controller.updatePurchaseProgress.bind(controller)
);

/**
 * @swagger
 * /api/master/csm/purchase-orders/{id}/set-cost-total:
 *   put:
 *     summary: 设置订单成本总价
 *     description: 设置指定采购订单的成本总价并计算毛利率
 *     tags: [采购订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 采购订单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - costTotal
 *             properties:
 *               costTotal:
 *                 type: number
 *                 format: float
 *                 minimum: 0
 *                 description: 成本总价
 *               costType:
 *                 type: string
 *                 enum: [manual, auto, calculated]
 *                 default: manual
 *                 description: 成本类型
 *     responses:
 *       200:
 *         description: 订单成本总价设置成功
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 采购订单不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.put(
  "/:id/set-cost-total",
  controller.setCostTotal.bind(controller)
);

/**
 * @swagger
 * /api/v1/master/csm/purchase-orders/product/{productId}/set-cost-total:
 *   put:
 *     tags: [采购订单管理]
 *     summary: 设置商品项成本总价
 *     description: 设置采购订单中单个商品项的成本总价
 *     parameters:
 *       - in: path
 *         name: productId
 *         required: true
 *         schema:
 *           type: string
 *         description: 商品项ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - costTotal
 *             properties:
 *               costTotal:
 *                 type: number
 *                 description: 成本总价
 *                 example: 100.50
 *     responses:
 *       200:
 *         description: 设置成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "设置商品项成本总价成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: 商品项ID
 *                     costTotal:
 *                       type: number
 *                       description: 设置的成本总价
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 商品项不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.put(
  "/product/:productId/set-cost-total",
  controller.setProductCostTotal.bind(controller)
);

/**
 * @swagger
 * /api/v1/master/csm/purchase-orders/product/{productId}/set-loss-reason:
 *   put:
 *     tags: [采购订单管理]
 *     summary: 设置商品项亏损原因
 *     description: 设置采购订单中单个商品项的亏损原因
 *     parameters:
 *       - in: path
 *         name: productId
 *         required: true
 *         schema:
 *           type: string
 *         description: 商品项ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - lossReason
 *             properties:
 *               lossReason:
 *                 type: string
 *                 description: 亏损原因
 *                 example: "freight_loss"
 *     responses:
 *       200:
 *         description: 设置成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "设置商品项亏损原因成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: 商品项ID
 *                     lossReason:
 *                       type: string
 *                       description: 设置的亏损原因
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 商品项不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.put(
  "/product/:productId/set-loss-reason",
  controller.setProductLossReason.bind(controller)
);

/**
 * @swagger
 * /api/master/csm/purchase-orders/{id}/set-actual-supplier:
 *   put:
 *     summary: 设置实际供应商
 *     description: 设置指定采购订单商品项的实际供应商（商品级别操作）
 *     tags: [采购订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 采购订单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - supplierName
 *               - itemId
 *             properties:
 *               supplierId:
 *                 type: integer
 *                 description: 供应商ID
 *               supplierName:
 *                 type: string
 *                 description: 供应商名称
 *               supplierCode:
 *                 type: string
 *                 description: 供应商编码
 *               itemId:
 *                 type: integer
 *                 description: 商品项ID（必填，供应商设置是商品级别的操作）
 *     responses:
 *       200:
 *         description: 实际供应商设置成功
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 采购订单不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.put(
  "/:id/set-actual-supplier",
  controller.setActualSupplier.bind(controller)
);

/**
 * @swagger
 * /api/master/csm/purchase-orders/{id}/update-tax-classification:
 *   put:
 *     summary: 更新税收分类
 *     description: 更新指定采购订单商品项的税收分类
 *     tags: [采购订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 采购订单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - itemId
 *               - taxCategory
 *             properties:
 *               itemId:
 *                 type: integer
 *                 description: 商品项ID
 *               taxCategory:
 *                 type: string
 *                 description: 税收分类
 *               taxClassificationCode:
 *                 type: string
 *                 description: 税收分类编码
 *               taxClassificationName:
 *                 type: string
 *                 description: 税收分类名称
 *     responses:
 *       200:
 *         description: 税收分类更新成功
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 采购订单或商品项不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.put(
  "/:id/update-tax-classification",
  controller.updateTaxClassification.bind(controller)
);

/**
 * @swagger
 * /api/master/csm/purchase-orders/{id}/save-loss-reason:
 *   put:
 *     summary: 保存亏损原因
 *     description: 保存指定采购订单的亏损原因并设置亏损状态
 *     tags: [采购订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 采购订单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - lossReason
 *             properties:
 *               lossReason:
 *                 type: string
 *                 description: 亏损原因
 *               lossType:
 *                 type: string
 *                 enum: [freight_loss, quote_error, quality_issue, market_change, other]
 *                 default: other
 *                 description: 亏损类型
 *     responses:
 *       200:
 *         description: 亏损原因保存成功
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 采购订单不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.put(
  "/:id/save-loss-reason",
  controller.saveLossReason.bind(controller)
);

/**
 * @swagger
 * /v1/master/csm/purchase-orders/{id}/set-expected-delivery-time:
 *   put:
 *     summary: 设置货期时间
 *     description: 设置采购订单或商品项的预期交货时间
 *     tags: [采购订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 采购订单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - expected_delivery_time
 *             properties:
 *               expected_delivery_time:
 *                 type: string
 *                 pattern: '^\d{13}$'
 *                 description: 预期交货时间（13位时间戳）
 *                 example: "1704067200000"
 *               itemId:
 *                 type: integer
 *                 description: 商品项ID（可选，如果指定则只更新该商品项）
 *     responses:
 *       200:
 *         description: 货期时间设置成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "订单预期交货时间更新成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       description: 订单ID或商品项ID
 *                     expected_delivery_time:
 *                       type: string
 *                       format: date-time
 *                       description: 预期交货时间
 *                     updated_at:
 *                       type: string
 *                       format: date-time
 *                       description: 更新时间
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 采购订单或商品项不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.put(
  "/:id/set-expected-delivery-time",
  controller.setExpectedDeliveryTime.bind(controller)
);

/**
 * @swagger
 * /api/master/csm/purchase-orders/{id}/generate-shipping-link:
 *   post:
 *     summary: 生成发货链接
 *     description: 为采购订单生成发货链接，包含收货人信息和发货类型
 *     tags: [采购订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 采购订单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - recipientName
 *               - recipientPhone
 *               - recipientAddress
 *             properties:
 *               recipientName:
 *                 type: string
 *                 description: 收货人姓名
 *                 example: "张三"
 *               recipientPhone:
 *                 type: string
 *                 description: 收货人电话
 *                 pattern: "^1[3-9]\\d{9}$"
 *                 example: "13800138000"
 *               recipientAddress:
 *                 type: string
 *                 description: 收货地址
 *                 example: "北京市朝阳区xxx街道xxx号"
 *               shippingType:
 *                 type: integer
 *                 description: 发货类型：1-快递物流 2-自定义物流 3-商家自送 4-线下自取 5-无需物流
 *                 enum: [1, 2, 3, 4, 5]
 *                 default: 1
 *                 example: 1
 *     responses:
 *       200:
 *         description: 发货链接生成成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "发货链接生成成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       description: 发货链接记录ID
 *                     shippingLink:
 *                       type: string
 *                       description: 生成的发货链接
 *                       example: "https://example.com/h5/SupplyChain/HavedShipped/ORDER123456"
 *                     recipientName:
 *                       type: string
 *                       description: 收货人姓名
 *                     recipientPhone:
 *                       type: string
 *                       description: 收货人电话
 *                     recipientAddress:
 *                       type: string
 *                       description: 收货地址
 *                     shippingType:
 *                       type: integer
 *                       description: 发货类型
 *                     generatedAt:
 *                       type: string
 *                       format: date-time
 *                       description: 生成时间
 *       400:
 *         description: 请求参数错误
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 400
 *                 message:
 *                   type: string
 *                   example: "收货人姓名不能为空"
 *       404:
 *         description: 采购订单不存在
 *       409:
 *         description: 该采购订单已生成发货链接，不能重复生成
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.post(
  "/:id/generate-shipping-link",
  controller.generateShippingLink.bind(controller)
);

module.exports = router;
