-- 创建采购订单发货链接表
-- 迁移时间: 2025-01-10
-- 描述: 为采购订单系统添加发货链接管理功能

-- 创建采购订单发货链接表
CREATE TABLE IF NOT EXISTS csm.purchase_order_shipping_link (
    id BIGSERIAL PRIMARY KEY,
    
    -- 链接信息
    shipping_link VARCHAR(500) NOT NULL COMMENT '发货链接',
    qr_code_url VARCHAR(500) COMMENT '发货二维码地址',
    
    -- 订单关联信息
    original_order_number VARCHAR(64) NOT NULL COMMENT '原订单号',
    purchase_order_id BIGINT NOT NULL COMMENT '采购订单ID',
    purchase_order_number VARCHAR(64) NOT NULL COMMENT '采购订单号',
    split_order_id BIGINT COMMENT '拆分订单ID（可为空）',
    split_order_number VARCHAR(64) COMMENT '拆分订单号（可为空）',
    
    -- 收货信息
    recipient_name VARCHAR(100) NOT NULL COMMENT '收货人',
    recipient_phone VARCHAR(20) NOT NULL COMMENT '收货人电话',
    recipient_address TEXT NOT NULL COMMENT '收货地址',
    
    -- 发货类型：1-快递物流 2-自定义物流 3-商家自送 4-线下自取 5-无需物流
    shipping_type INT NOT NULL DEFAULT 1 COMMENT '发货类型：1-快递物流 2-自定义物流 3-商家自送 4-线下自取 5-无需物流',
    
    -- 操作人信息
    operator_id BIGINT NOT NULL COMMENT '操作人ID',
    operator_name VARCHAR(100) NOT NULL COMMENT '操作人姓名',
    
    -- 时间信息
    generated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '生成时间',
    
    -- 系统字段
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间'
);

-- 添加外键约束
ALTER TABLE csm.purchase_order_shipping_link 
ADD CONSTRAINT fk_shipping_link_purchase_order_id 
FOREIGN KEY (purchase_order_id) REFERENCES csm.purchase_order(id);

ALTER TABLE csm.purchase_order_shipping_link 
ADD CONSTRAINT fk_shipping_link_split_order_id 
FOREIGN KEY (split_order_id) REFERENCES csm.purchase_order_split(id);

-- 创建唯一约束（确保一对一关系）
ALTER TABLE csm.purchase_order_shipping_link
ADD CONSTRAINT uk_shipping_link_purchase_order_id UNIQUE (purchase_order_id);

ALTER TABLE csm.purchase_order_shipping_link
ADD CONSTRAINT uk_shipping_link_split_order_id UNIQUE (split_order_id);

-- 创建索引
CREATE INDEX idx_shipping_link_original_order_number ON csm.purchase_order_shipping_link(original_order_number);
CREATE INDEX idx_shipping_link_purchase_order_number ON csm.purchase_order_shipping_link(purchase_order_number);
CREATE INDEX idx_shipping_link_split_order_number ON csm.purchase_order_shipping_link(split_order_number);
CREATE INDEX idx_shipping_link_operator_id ON csm.purchase_order_shipping_link(operator_id);
CREATE INDEX idx_shipping_link_shipping_type ON csm.purchase_order_shipping_link(shipping_type);
CREATE INDEX idx_shipping_link_generated_at ON csm.purchase_order_shipping_link(generated_at);
CREATE INDEX idx_shipping_link_created_at ON csm.purchase_order_shipping_link(created_at);
CREATE INDEX idx_shipping_link_deleted_at ON csm.purchase_order_shipping_link(deleted_at);

-- 添加表注释
ALTER TABLE csm.purchase_order_shipping_link COMMENT = '采购订单发货链接表 - 管理采购订单的发货链接和二维码信息，与采购订单和拆分订单为一对一关系';
