// 采购订单发货链接表
model purchase_order_shipping_link {
  id                        BigInt                   @id @default(autoincrement()) // 发货链接ID，主键，自增长
  
  // 链接信息
  shipping_link             String                   @db.VarChar(500) // 发货链接
  qr_code_url               String?                  @db.VarChar(500) // 发货二维码地址
  
  // 订单关联信息
  original_order_number     String                   @db.VarChar(64) // 原订单号
  purchase_order_id         BigInt                   // 采购订单ID
  purchase_order_number     String                   @db.VarChar(64) // 采购订单号
  split_order_id            BigInt?                  // 拆分订单ID（可为空）
  split_order_number        String?                  @db.VarChar(64) // 拆分订单号（可为空）
  
  // 收货信息
  recipient_name            String                   @db.VarChar(100) // 收货人
  recipient_phone           String                   @db.VarChar(20) // 收货人电话
  recipient_address         String                   @db.Text // 收货地址
  
  // 发货类型：1-快递物流 2-自定义物流 3-商家自送 4-线下自取 5-无需物流
  shipping_type             Int                      @default(1) // 发货类型
  
  // 操作人信息
  operator_id               BigInt                   // 操作人ID
  operator_name             String                   @db.VarChar(100) // 操作人姓名
  
  // 时间信息
  generated_at              DateTime                 @default(now()) // 生成时间
  
  // 系统字段
  created_at                DateTime                 @default(now()) // 创建时间
  updated_at                DateTime                 @updatedAt // 更新时间
  deleted_at                DateTime? // 删除时间
  
  // 关联关系
  purchase_order            purchase_order           @relation(fields: [purchase_order_id], references: [id], map: "fk_shipping_link_purchase_order_id") // 关联采购订单
  split_order               purchase_order_split?    @relation(fields: [split_order_id], references: [id], map: "fk_shipping_link_split_order_id") // 关联拆分订单（可选）
  
  @@index([purchase_order_id], map: "idx_shipping_link_purchase_order_id")
  @@index([split_order_id], map: "idx_shipping_link_split_order_id")
  @@index([original_order_number], map: "idx_shipping_link_original_order_number")
  @@index([purchase_order_number], map: "idx_shipping_link_purchase_order_number")
  @@index([split_order_number], map: "idx_shipping_link_split_order_number")
  @@index([operator_id], map: "idx_shipping_link_operator_id")
  @@index([shipping_type], map: "idx_shipping_link_shipping_type")
  @@index([generated_at], map: "idx_shipping_link_generated_at")
  @@index([created_at], map: "idx_shipping_link_created_at")
  @@index([deleted_at], map: "idx_shipping_link_deleted_at")
  @@schema("csm")
}
