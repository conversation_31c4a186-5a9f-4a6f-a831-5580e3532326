# 采购订单发货链接生成功能测试

## 功能概述
为采购订单系统添加发货链接生成功能，支持一对一的发货链接管理。

## 实现内容

### 1. 数据库设计
- 创建了 `purchase_order_shipping_link` 表
- 建立了与 `purchase_order` 和 `purchase_order_split` 的一对一关系
- 添加了唯一约束确保数据完整性

### 2. 后端API
- 在 `PurchaseOrderController` 中添加了 `generateShippingLink` 方法
- 在路由中添加了 `POST /:id/generate-shipping-link` 接口
- 支持参数验证、权限检查和错误处理

### 3. 前端集成
- 在 `purchaseOrder.js` API文件中添加了 `generateShippingLink` 方法
- 修改了前端组件的生成链接逻辑，调用后端API
- 保持了原有的UI交互流程

## API接口详情

### 请求
```
POST /api/v1/master/csm/purchase-orders/{id}/generate-shipping-link
```

### 请求参数
```json
{
  "recipientName": "张三",
  "recipientPhone": "13800138000", 
  "recipientAddress": "北京市朝阳区xxx街道xxx号",
  "shippingType": 1
}
```

### 响应
```json
{
  "code": 200,
  "message": "发货链接生成成功",
  "data": {
    "id": 1,
    "shippingLink": "https://example.com/h5/SupplyChain/HavedShipped/ORDER123456",
    "recipientName": "张三",
    "recipientPhone": "13800138000",
    "recipientAddress": "北京市朝阳区xxx街道xxx号",
    "shippingType": 1,
    "generatedAt": "2025-01-10T10:30:00.000Z"
  }
}
```

## 生成的链接格式
```
[当前域名]/h5/SupplyChain/HavedShipped/:orderid
```

其中 `:orderid` 为原订单号 (`original_order_number`)

## 业务规则
1. 只有 `link_status = 1` 的采购订单才能生成链接
2. 每个采购订单只能生成一个发货链接（一对一关系）
3. 收货人信息必填且格式验证
4. 支持5种发货类型：快递物流、自定义物流、商家自送、线下自取、无需物流

## 测试建议
1. 测试正常的链接生成流程
2. 测试重复生成链接的错误处理
3. 测试参数验证（空值、格式错误等）
4. 测试权限控制
5. 测试不同发货类型的处理
6. 测试链接格式的正确性

## 注意事项
- 确保数据库迁移脚本已执行
- 确保前端已导入新的API方法
- 确保路由配置正确
- 测试时注意检查生成的链接域名是否正确
