# 采购订单发货链接生成功能测试

## 功能概述
为采购订单系统添加发货链接生成功能，支持一对一的发货链接管理。

## 实现内容

### 1. 数据库设计
- 创建了 `purchase_order_shipping_link` 表
- 建立了与 `purchase_order` 和 `purchase_order_split` 的一对一关系
- 添加了唯一约束确保数据完整性

### 2. 后端API
- 在 `PurchaseOrderController` 中添加了 `generateShippingLink` 方法
- 在路由中添加了 `POST /:id/generate-shipping-link` 接口
- 支持参数验证、权限检查和错误处理
- **新增功能**：
  - 从请求头获取前端域名
  - 使用QRCode库生成二维码
  - 调用文件上传服务上传二维码图片
  - 保存二维码图片地址到数据库

### 3. 前端集成
- 在 `purchaseOrder.js` API文件中添加了 `generateShippingLink` 方法
- 修改了前端组件的生成链接逻辑，调用后端API
- 保持了原有的UI交互流程
- **新增功能**：处理返回的二维码图片地址

## API接口详情

### 请求
```
POST /api/v1/master/csm/purchase-orders/{id}/generate-shipping-link
```

### 请求参数
```json
{
  "recipientName": "张三",
  "recipientPhone": "13800138000", 
  "recipientAddress": "北京市朝阳区xxx街道xxx号",
  "shippingType": 1
}
```

### 响应
```json
{
  "code": 200,
  "message": "发货链接生成成功",
  "data": {
    "id": 1,
    "shippingLink": "https://example.com/h5/SupplyChain/HavedShipped/ORDER123456",
    "qrCodeUrl": "https://example.com/uploads/qrcodes/shipping/qrcode_PO123456_1641801600000.png",
    "recipientName": "张三",
    "recipientPhone": "13800138000",
    "recipientAddress": "北京市朝阳区xxx街道xxx号",
    "shippingType": 1,
    "generatedAt": "2025-01-10T10:30:00.000Z"
  }
}
```

## 生成的链接格式
```
[前端当前域名]/h5/SupplyChain/HavedShipped/:orderid
```

其中 `:orderid` 为原订单号 (`original_order_number`)

## 二维码生成
- 使用QRCode库生成PNG格式的二维码
- 二维码尺寸：300x300像素
- 自动上传到文件存储服务
- 文件路径：`qrcodes/shipping/qrcode_{采购订单号}_{时间戳}.png`

## 业务规则
1. 只有 `link_status = 1` 的采购订单才能生成链接
2. 每个采购订单只能生成一个发货链接（一对一关系）
3. 收货人信息必填且格式验证
4. 支持5种发货类型：快递物流、自定义物流、商家自送、线下自取、无需物流

## 测试建议
1. 测试正常的链接生成流程
2. 测试重复生成链接的错误处理
3. 测试参数验证（空值、格式错误等）
4. 测试权限控制
5. 测试不同发货类型的处理
6. 测试链接格式的正确性
7. **新增测试**：
   - 测试二维码生成功能
   - 测试文件上传功能
   - 测试前端域名获取
   - 测试二维码图片访问

## 注意事项
- 确保数据库迁移脚本已执行
- 确保前端已导入新的API方法
- 确保路由配置正确
- 测试时注意检查生成的链接域名是否正确
- **新增注意事项**：
  - 确保qrcode库已安装
  - 确保文件上传服务配置正确
  - 确保上传目录权限正确
  - 测试时检查二维码图片是否正确生成和上传

## 复制链接功能

### 新增功能
当用户点击"复制链接"按钮时，系统会：
1. 调用后端API获取该订单的发货链接数据
2. 在弹窗中显示完整的发货信息：
   - 真实的二维码图片
   - 采购订单号和原订单号
   - 收货人信息（姓名、电话、地址）
   - 链接生成时间
   - 可复制的发货链接

### API接口
```
GET /api/v1/master/csm/purchase-orders/{id}/shipping-link
```

### 前端实现
- 修改了`handleCopyLink`方法，先请求发货链接数据
- 更新了复制链接弹窗模板，显示真实数据
- 支持复制订单号和发货链接
- 显示真实的二维码图片

## 依赖库
- `qrcode`: 用于生成二维码
- 文件上传服务：用于上传二维码图片

## 完整流程
1. **生成链接**：用户填写收货信息 → 后端生成链接和二维码 → 上传二维码图片 → 保存到数据库
2. **复制链接**：用户点击复制链接 → 前端请求发货链接数据 → 显示完整信息弹窗 → 用户可复制链接或订单号
