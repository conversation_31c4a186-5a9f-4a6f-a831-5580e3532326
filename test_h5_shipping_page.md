# H5发货页面对接测试

## 测试场景

### 1. 采购订单发货链接
**测试步骤**：
1. 创建一个采购订单
2. 为该订单生成发货链接
3. 访问H5页面：`/h5/SupplyChain/HavedShipped/{linkId}`
4. 验证页面显示的数据

**预期结果**：
- 显示采购订单的商品信息
- 显示收货人信息
- 支持商品详情页面跳转

### 2. 拆分订单发货链接
**测试步骤**：
1. 创建一个采购订单并拆分
2. 为拆分订单生成发货链接
3. 访问H5页面：`/h5/SupplyChain/HavedShipped/{linkId}`
4. 验证页面显示的数据

**预期结果**：
- 显示拆分订单的商品信息（不是原订单的商品）
- 显示收货人信息
- 支持商品详情页面跳转

### 3. 多商品显示
**测试步骤**：
1. 创建包含多个商品的订单
2. 生成发货链接
3. 访问H5页面
4. 验证多商品显示

**预期结果**：
- 以列表形式显示所有商品
- 每个商品都可以点击跳转到详情页
- 界面布局清晰美观

## API测试

### 接口地址
```
GET /api/v1/master/csm/purchase-orders/shipping-page/{linkId}
```

### 测试用例

#### 1. 正常情况
**请求**：`GET /api/v1/master/csm/purchase-orders/shipping-page/123`

**预期响应**：
```json
{
  "code": 200,
  "message": "获取发货页面数据成功",
  "data": {
    "linkId": 123,
    "shippingLink": "https://frontend.com/h5/SupplyChain/HavedShipped/123",
    "qrCodeUrl": "https://backend.com/uploads/qrcodes/shipping/qrcode_PO123456_123_1641801600000.png",
    "receiverName": "张三",
    "receiverPhone": "13800138000",
    "receiverAddress": "北京市朝阳区xxx街道xxx号",
    "shippingType": 1,
    "generatedAt": "2025-01-10T10:30:00.000Z",
    "originalOrderNumber": "ORDER123456",
    "purchaseOrderNumber": "PO123456",
    "splitOrderNumber": null,
    "orderInfo": {
      "orderType": "purchase",
      "orderId": 456,
      "orderNumber": "PO123456",
      "createTime": "2025-01-10T09:00:00.000Z",
      "totalAmount": 1000.00,
      "status": 1,
      "supplierName": "供应商名称"
    },
    "products": [
      {
        "id": 1,
        "productId": 789,
        "productCode": "100078596122",
        "productName": "商品名称",
        "productCategory": "模拟器材",
        "productBrand": "品牌名称",
        "quantity": 2,
        "unitPrice": 500.00,
        "totalPrice": 1000.00,
        "specifications": "规格信息",
        "productImage": "https://example.com/image.jpg"
      }
    ]
  }
}
```

#### 2. 链接不存在
**请求**：`GET /api/v1/master/csm/purchase-orders/shipping-page/999999`

**预期响应**：
```json
{
  "code": 404,
  "message": "发货链接不存在"
}
```

#### 3. 参数错误
**请求**：`GET /api/v1/master/csm/purchase-orders/shipping-page/abc`

**预期响应**：
```json
{
  "code": 400,
  "message": "发货链接ID不能为空"
}
```

## 前端测试

### 页面功能测试
1. **加载状态**：页面加载时显示loading动画
2. **数据显示**：正确显示订单和商品信息
3. **商品跳转**：点击商品信息能正确跳转到详情页
4. **响应式布局**：在不同设备上显示正常
5. **错误处理**：网络错误时显示友好提示

### 兼容性测试
1. **单商品**：只有一个商品时的显示效果
2. **多商品**：多个商品时的列表显示效果
3. **无商品**：没有商品数据时的提示信息
4. **长文本**：商品名称很长时的显示效果

## 注意事项

1. **数据安全**：链接ID不会泄露敏感的订单信息
2. **性能优化**：一次请求获取所有必要数据，减少网络请求
3. **用户体验**：加载状态和错误提示提升用户体验
4. **扩展性**：支持未来添加更多商品信息字段

## 测试清单

- [ ] 采购订单发货链接正常显示
- [ ] 拆分订单发货链接正常显示
- [ ] 多商品列表显示正常
- [ ] 商品详情页跳转正常
- [ ] 加载状态显示正常
- [ ] 错误处理正常
- [ ] 移动端显示正常
- [ ] API接口返回数据正确
- [ ] 数据库查询逻辑正确
- [ ] 权限控制正常（如果需要）
